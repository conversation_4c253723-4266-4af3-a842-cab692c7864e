const { OpenAI } = require('openai')
const config = require('./config')
const { customLog } = require('./logger')

class AzureOpenAIService {
  constructor() {
    this.client = null
    this.init()
  }

  init() {
    try {
      // Validate configuration
      if (!config.AZURE_OPENAI_CONFIG.endpoint) {
        throw new Error('Azure OpenAI endpoint not configured')
      }

      if (!config.AZURE_OPENAI_CONFIG.apiKey) {
        throw new Error('Azure OpenAI API key not configured')
      }

      // Initialize Azure OpenAI client
      this.client = new OpenAI({
        apiKey: config.AZURE_OPENAI_CONFIG.apiKey,
        baseURL: `${config.AZURE_OPENAI_CONFIG.endpoint}/openai/deployments/${config.AZURE_OPENAI_CONFIG.deploymentName}`,
        defaultQuery: { 'api-version': config.AZURE_OPENAI_CONFIG.apiVersion },
        defaultHeaders: {
          'api-key': config.AZURE_OPENAI_CONFIG.apiKey,
        }
      })

      customLog('Azure OpenAI Service initialized successfully')

    } catch (error) {
      customLog('Error initializing Azure OpenAI Service:', error.message)
      throw error
    }
  }

  async generateReply(conversationHistory) {
    // Validate input
    if (!Array.isArray(conversationHistory)) {
      throw new Error('Conversation history must be an array')
    }

    if (conversationHistory.length === 0) {
      throw new Error('Conversation history cannot be empty')
    }

    // Validate client is initialized
    if (!this.client) {
      throw new Error('Azure OpenAI client not initialized')
    }

    try {
      // Format conversation history for OpenAI format
      const messages = [
        {
          role: 'system',
          content: config.AI_PROMPT
        },
        ...conversationHistory.map((turn, index) => {
          if (!turn || typeof turn !== 'object') {
            throw new Error(`Invalid conversation turn at index ${index}`)
          }

          if (typeof turn.content !== 'string' || turn.content.trim() === '') {
            throw new Error(`Invalid or empty content at conversation turn ${index}`)
          }

          return {
            role: turn.user ? 'user' : 'assistant',
            content: turn.content.trim()
          }
        })
      ]

      customLog('Sending request to Azure OpenAI with', messages.length, 'messages')

      // Call Azure OpenAI
      const response = await this.client.chat.completions.create({
        model: config.AZURE_OPENAI_CONFIG.deploymentName, // This should match your deployment name
        messages: messages,
        max_tokens: 500,
        temperature: 0.7,
        top_p: 0.9,
        frequency_penalty: 0,
        presence_penalty: 0,
        stop: null,
        // Azure-specific parameters
        response_format: { type: 'text' },
        stream: false
      })

      // Validate response structure
      if (!response || !response.choices || response.choices.length === 0) {
        throw new Error('Invalid response structure from Azure OpenAI')
      }

      const choice = response.choices[0]
      if (!choice.message || !choice.message.content) {
        throw new Error('Invalid message structure in Azure OpenAI response')
      }

      const responseText = choice.message.content.trim()
      if (typeof responseText !== 'string' || responseText === '') {
        throw new Error('Azure OpenAI returned empty response')
      }

      // Log usage information
      if (response.usage) {
        customLog('Azure OpenAI usage:', {
          prompt_tokens: response.usage.prompt_tokens,
          completion_tokens: response.usage.completion_tokens,
          total_tokens: response.usage.total_tokens
        })
      }

      return responseText

    } catch (error) {
      customLog('Error generating Azure OpenAI reply:', error.message || error)

      // Re-throw with more context for different error types
      if (error.message && error.message.includes('quota')) {
        throw new Error('Azure OpenAI quota exceeded. Please try again later.')
      } else if (error.message && error.message.includes('rate limit')) {
        throw new Error('Azure OpenAI rate limit exceeded. Please try again later.')
      } else if (error.message && error.message.includes('authentication')) {
        throw new Error('Azure OpenAI authentication failed. Please check configuration.')
      } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
        throw new Error('Network error connecting to Azure OpenAI. Please check your connection.')
      } else if (error.status === 401) {
        throw new Error('Azure OpenAI authentication failed. Please check your API key.')
      } else if (error.status === 403) {
        throw new Error('Azure OpenAI access forbidden. Please check your permissions.')
      } else if (error.status === 429) {
        throw new Error('Azure OpenAI rate limit exceeded. Please try again later.')
      } else if (error.status >= 500) {
        throw new Error('Azure OpenAI service error. Please try again later.')
      } else {
        throw new Error(`Azure OpenAI service error: ${error.message || 'Unknown error occurred'}`)
      }
    }
  }

  // Method to test the connection
  async testConnection() {
    try {
      const testResponse = await this.client.chat.completions.create({
        model: config.AZURE_OPENAI_CONFIG.deploymentName,
        messages: [
          { role: 'system', content: 'You are a helpful assistant.' },
          { role: 'user', content: 'Say hello' }
        ],
        max_tokens: 10
      })

      customLog('Azure OpenAI connection test successful')
      return true

    } catch (error) {
      customLog('Azure OpenAI connection test failed:', error.message)
      return false
    }
  }

  // Get service status
  getStatus() {
    return {
      isInitialized: this.client !== null,
      endpoint: config.AZURE_OPENAI_CONFIG.endpoint,
      deploymentName: config.AZURE_OPENAI_CONFIG.deploymentName,
      apiVersion: config.AZURE_OPENAI_CONFIG.apiVersion
    }
  }
}

module.exports = { AzureOpenAIService }
