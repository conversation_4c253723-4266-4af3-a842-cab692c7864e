const sdk = require('microsoft-cognitiveservices-speech-sdk')
const { PassThrough } = require('stream')
const config = require('./config')
const { customLog } = require('./logger')

class AzureSpeechService {
  constructor() {
    this.speechConfig = null
    this.audioConfig = null
    this.recognizer = null
    this.pushStream = null
    this.isRecognizing = false

    this.init()
  }

  init() {
    try {
      // Validate configuration
      if (!config.AZURE_SPEECH_CONFIG.subscriptionKey) {
        throw new Error('Azure Speech subscription key not configured')
      }

      if (!config.AZURE_SPEECH_CONFIG.region) {
        throw new Error('Azure Speech region not configured')
      }

      // Create speech configuration
      this.speechConfig = sdk.SpeechConfig.fromSubscription(
        config.AZURE_SPEECH_CONFIG.subscriptionKey,
        config.AZURE_SPEECH_CONFIG.region
      )

      // Configure speech recognition settings
      this.speechConfig.speechRecognitionLanguage = config.AZURE_SPEECH_CONFIG.language
      this.speechConfig.outputFormat = sdk.OutputFormat[config.AZURE_SPEECH_CONFIG.format]
      this.speechConfig.setProfanity(sdk.ProfanityOption[config.AZURE_SPEECH_CONFIG.profanity])

      // Enable additional features
      this.speechConfig.requestWordLevelTimestamps()

      if (config.AZURE_SPEECH_CONFIG.enableDictation) {
        this.speechConfig.enableDictation()
      }

      customLog('Azure Speech Service initialized successfully')

    } catch (error) {
      customLog('Error initializing Azure Speech Service:', error.message)
      throw error
    }
  }

  createRecognizer(onResult, onError, onSessionStopped) {
    try {
      // Create push stream for audio input
      this.pushStream = sdk.AudioInputStream.createPushStream()

      // Create audio configuration from push stream
      this.audioConfig = sdk.AudioConfig.fromStreamInput(this.pushStream)

      // Create speech recognizer
      this.recognizer = new sdk.SpeechRecognizer(this.speechConfig, this.audioConfig)

      // Configure event handlers
      this.recognizer.recognizing = (s, e) => {
        if (e.result.reason === sdk.ResultReason.RecognizingSpeech) {
          const text = e.result.text.trim()
          if (text && onResult) {
            onResult({
              text: text,
              isFinal: false,
              confidence: e.result.properties?.getProperty(sdk.PropertyId.SpeechServiceResponse_JsonResult)
            })
          }
        }
      }

      this.recognizer.recognized = (s, e) => {
        if (e.result.reason === sdk.ResultReason.RecognizedSpeech) {
          const text = e.result.text.trim()
          if (text && onResult) {
            onResult({
              text: text,
              isFinal: true,
              confidence: e.result.properties?.getProperty(sdk.PropertyId.SpeechServiceResponse_JsonResult),
              duration: e.result.duration,
              offset: e.result.offset
            })
          }
        } else if (e.result.reason === sdk.ResultReason.NoMatch) {
          customLog('Azure Speech: No speech could be recognized')
        }
      }

      this.recognizer.canceled = (s, e) => {
        customLog('Azure Speech recognition canceled:', e.reason)

        if (e.reason === sdk.CancellationReason.Error) {
          const errorMessage = `Speech recognition error: ${e.errorDetails}`
          customLog(errorMessage)
          if (onError) {
            onError(new Error(errorMessage))
          }
        }

        this.stopRecognition()
      }

      this.recognizer.sessionStopped = (s, e) => {
        customLog('Azure Speech session stopped')
        this.isRecognizing = false
        if (onSessionStopped) {
          onSessionStopped()
        }
      }

      this.recognizer.sessionStarted = (s, e) => {
        customLog('Azure Speech session started')
        this.isRecognizing = true
      }

      return this.recognizer

    } catch (error) {
      customLog('Error creating Azure Speech recognizer:', error.message)
      throw error
    }
  }

  startContinuousRecognition() {
    if (!this.recognizer) {
      throw new Error('Speech recognizer not initialized')
    }

    try {
      this.recognizer.startContinuousRecognitionAsync(
        () => {
          customLog('Azure Speech continuous recognition started')
          this.isRecognizing = true
        },
        (error) => {
          customLog('Error starting Azure Speech recognition:', error)
          this.isRecognizing = false
          throw new Error(`Failed to start speech recognition: ${error}`)
        }
      )
    } catch (error) {
      customLog('Error in startContinuousRecognition:', error.message)
      throw error
    }
  }

  stopRecognition() {
    if (this.recognizer && this.isRecognizing) {
      try {
        this.recognizer.stopContinuousRecognitionAsync(
          () => {
            customLog('Azure Speech recognition stopped')
            this.isRecognizing = false
          },
          (error) => {
            customLog('Error stopping Azure Speech recognition:', error)
          }
        )
      } catch (error) {
        customLog('Error in stopRecognition:', error.message)
      }
    }
  }

  writeAudioData(audioBuffer) {
    if (!this.pushStream) {
      throw new Error('Push stream not initialized')
    }

    try {
      // Validate audio buffer
      if (!audioBuffer || audioBuffer.length === 0) {
        customLog('Warning: Received empty audio buffer')
        return
      }

      // Write audio data to push stream
      this.pushStream.write(audioBuffer)

    } catch (error) {
      customLog('Error writing audio data to Azure Speech stream:', error.message)
      throw error
    }
  }

  closeStream() {
    if (this.pushStream) {
      try {
        this.pushStream.close()
        customLog('Azure Speech push stream closed')
      } catch (error) {
        customLog('Error closing Azure Speech stream:', error.message)
      }
    }
  }

  cleanup() {
    try {
      this.stopRecognition()

      if (this.recognizer) {
        this.recognizer.close()
        this.recognizer = null
      }

      this.closeStream()
      this.pushStream = null

      if (this.audioConfig) {
        this.audioConfig.close()
        this.audioConfig = null
      }

      customLog('Azure Speech Service cleaned up')

    } catch (error) {
      customLog('Error during Azure Speech cleanup:', error.message)
    }
  }

  // Utility method to check if service is ready
  isReady() {
    return this.speechConfig !== null && !this.isRecognizing
  }

  // Get current recognition state
  getRecognitionState() {
    return {
      isRecognizing: this.isRecognizing,
      hasRecognizer: this.recognizer !== null,
      hasStream: this.pushStream !== null
    }
  }
}

module.exports = { AzureSpeechService }
