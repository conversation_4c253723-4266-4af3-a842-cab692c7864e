const sdk = require('microsoft-cognitiveservices-speech-sdk')
const config = require('./config')
const { customLog } = require('./logger')

class AzureTTSService {
  constructor() {
    this.speechConfig = null
    this.init()
  }

  init() {
    try {
      // Validate configuration
      if (!config.AZURE_TTS_CONFIG.subscriptionKey) {
        throw new Error('Azure Speech subscription key not configured for TTS')
      }

      if (!config.AZURE_TTS_CONFIG.region) {
        throw new Error('Azure Speech region not configured for TTS')
      }

      // Create speech configuration for TTS
      this.speechConfig = sdk.SpeechConfig.fromSubscription(
        config.AZURE_TTS_CONFIG.subscriptionKey,
        config.AZURE_TTS_CONFIG.region
      )

      // Configure TTS settings
      this.speechConfig.speechSynthesisVoiceName = config.AZURE_TTS_CONFIG.voice
      this.speechConfig.speechSynthesisOutputFormat = sdk.SpeechSynthesisOutputFormat[config.AZURE_TTS_CONFIG.outputFormat]

      customLog('Azure TTS Service initialized successfully with voice:', config.AZURE_TTS_CONFIG.voice)

    } catch (error) {
      customLog('Error initializing Azure TTS Service:', error.message)
      throw error
    }
  }

  async generateSpeech(text) {
    // Validate input
    if (typeof text !== 'string') {
      throw new Error('Text input must be a string')
    }

    if (text.trim() === '') {
      throw new Error('Text input cannot be empty')
    }

    // Validate text length
    const maxTextLength = 5000
    if (text.length > maxTextLength) {
      throw new Error(`Text too long for speech generation: ${text.length} characters (max: ${maxTextLength})`)
    }

    if (!this.speechConfig) {
      throw new Error('Azure TTS not initialized')
    }

    try {
      // Clean text for better pronunciation
      const cleanedText = this.cleanTextForSpeech(text)

      // Create SSML for better control
      const ssml = this.createSSML(cleanedText)

      // Create synthesizer with null audio config to get audio data
      const synthesizer = new sdk.SpeechSynthesizer(this.speechConfig, null)

      return new Promise((resolve, reject) => {
        synthesizer.speakSsmlAsync(
          ssml,
          (result) => {
            try {
              if (result.reason === sdk.ResultReason.SynthesizingAudioCompleted) {
                customLog('Azure TTS: Speech synthesis completed successfully')

                // Convert ArrayBuffer to Buffer
                const audioBuffer = Buffer.from(result.audioData)

                if (audioBuffer.length === 0) {
                  throw new Error('Azure TTS returned empty audio data')
                }

                synthesizer.close()
                resolve(audioBuffer)

              } else if (result.reason === sdk.ResultReason.Canceled) {
                const cancellation = sdk.CancellationDetails.fromResult(result)
                const errorMessage = `Azure TTS canceled: ${cancellation.reason} - ${cancellation.errorDetails}`
                customLog('Azure TTS error:', errorMessage)

                synthesizer.close()
                reject(new Error(errorMessage))

              } else {
                const errorMessage = `Azure TTS unexpected result: ${result.reason}`
                customLog('Azure TTS error:', errorMessage)

                synthesizer.close()
                reject(new Error(errorMessage))
              }
            } catch (error) {
              synthesizer.close()
              reject(error)
            }
          },
          (error) => {
            customLog('Azure TTS synthesis error:', error)
            synthesizer.close()
            reject(new Error(`Azure TTS synthesis failed: ${error}`))
          }
        )
      })

    } catch (error) {
      customLog('Error generating speech with Azure TTS:', error.message)

      // Provide more specific error messages
      if (error.message && error.message.includes('quota')) {
        throw new Error('Azure TTS quota exceeded. Please try again later.')
      } else if (error.message && error.message.includes('rate limit')) {
        throw new Error('Azure TTS rate limit exceeded. Please try again later.')
      } else if (error.message && error.message.includes('authentication')) {
        throw new Error('Azure TTS authentication failed. Please check API key.')
      } else {
        throw new Error(`Azure TTS generation failed: ${error.message || 'Unknown error occurred'}`)
      }
    }
  }

  // Create SSML for better speech control
  createSSML(text) {
    const voice = config.AZURE_TTS_CONFIG.voice
    const speakingRate = config.AZURE_TTS_CONFIG.speakingRate
    const pitch = config.AZURE_TTS_CONFIG.pitch

    return `
      <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="es-ES">
        <voice name="${voice}">
          <prosody rate="${speakingRate}" pitch="${pitch}">
            ${this.escapeXml(text)}
          </prosody>
        </voice>
      </speak>
    `.trim()
  }

  // Clean text for better speech synthesis
  cleanTextForSpeech(text) {
    return text
      // Remove markdown links
      .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1')
      // Remove markdown bold/italic
      .replace(/\*\*([^*]+)\*\*/g, '$1')
      .replace(/\*([^*]+)\*/g, '$1')
      .replace(/__([^_]+)__/g, '$1')
      .replace(/_([^_]+)_/g, '$1')
      // Remove code blocks
      .replace(/`([^`]+)`/g, '$1')
      .replace(/```[\s\S]*?```/g, '')
      // Clean list markers
      .replace(/^[\s-*]+/gm, '')
      .replace(/^\s*\d+\.\s+/gm, '')
      // Remove quotes
      .replace(/^\s*>\s+/gm, '')
      // Remove headers
      .replace(/^#{1,6}\s+/gm, '')
      // Normalize whitespace
      .replace(/\s+/g, ' ')
      // Handle brand names for better pronunciation
      .replace(/Movistar\+/g, 'Movistar Plus')
      .trim()
  }

  // Escape XML special characters for SSML
  escapeXml(text) {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&apos;')
  }

  // Test TTS connection
  async testConnection() {
    try {
      const testAudio = await this.generateSpeech('Hola, esta es una prueba de Azure Text-to-Speech.')
      customLog('Azure TTS connection test successful, audio length:', testAudio.length)
      return true
    } catch (error) {
      customLog('Azure TTS connection test failed:', error.message)
      return false
    }
  }

  // Get available voices (requires additional API call)
  async getAvailableVoices() {
    try {
      const synthesizer = new sdk.SpeechSynthesizer(this.speechConfig, null)

      return new Promise((resolve, reject) => {
        synthesizer.getVoicesAsync(
          'es-ES', // Language filter
          (result) => {
            if (result.reason === sdk.ResultReason.VoicesListRetrieved) {
              const voices = result.voices.map(voice => ({
                name: voice.name,
                displayName: voice.localName,
                gender: voice.gender,
                locale: voice.locale
              }))
              synthesizer.close()
              resolve(voices)
            } else {
              synthesizer.close()
              reject(new Error('Failed to retrieve voices'))
            }
          },
          (error) => {
            synthesizer.close()
            reject(new Error(`Error retrieving voices: ${error}`))
          }
        )
      })
    } catch (error) {
      customLog('Error getting available voices:', error.message)
      throw error
    }
  }

  // Get service status
  getStatus() {
    return {
      isInitialized: this.speechConfig !== null,
      voice: config.AZURE_TTS_CONFIG.voice,
      region: config.AZURE_TTS_CONFIG.region,
      outputFormat: config.AZURE_TTS_CONFIG.outputFormat
    }
  }
}

module.exports = { AzureTTSService }
