/**
 * Centralized utilities export
 * Provides easy access to all utility functions
 */

// Core utilities
const { logger } = require('./logger')
const { performance } = require('./performance')
const { validation } = require('./validation')
const { helpers } = require('./helpers')

// Legacy compatibility - import from current modules directory
let cleanTextForSpeech
try {
  // Try to import from legacy location
  const textProcessor = require('../../../modules/textProcessor')
  cleanTextForSpeech = textProcessor.cleanTextForSpeech
} catch (error) {
  // Fallback to helpers implementation
  cleanTextForSpeech = helpers.cleanTextForSpeech
}

module.exports = {
  // Logging
  logger,

  // Performance monitoring
  performance,

  // Validation utilities
  validation,

  // General helpers
  helpers,

  // Text processing (legacy compatibility)
  cleanTextForSpeech,

  // Backward compatibility
  customLog: logger.customLog,

  // Convenience re-exports
  startTimer: performance.startTimer,
  endTimer: performance.endTimer,
  timeAsync: performance.timeAsync,
  validateAudioBuffer: validation.validateAudioBuffer,
  validateTextInput: validation.validateTextInput,
  generateUUID: helpers.generateUUID,
  deepClone: helpers.deepClone,
  retry: helpers.retry,
  sleep: helpers.sleep
}
