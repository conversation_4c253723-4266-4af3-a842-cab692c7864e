/**
 * General utility functions and helpers
 * Common operations used across the application
 */
const crypto = require('crypto')
const path = require('path')
const fs = require('fs').promises

class Helpers {
  // ===========================================
  // STRING UTILITIES
  // ===========================================

  /**
   * Generate a random string of specified length
   * @param {number} length - Length of the string
   * @param {string} charset - Character set to use
   * @returns {string} Random string
   */
  generateRandomString(length = 16, charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789') {
    let result = ''
    for (let i = 0; i < length; i++) {
      result += charset.charAt(Math.floor(Math.random() * charset.length))
    }
    return result
  }

  /**
   * Generate a secure random token
   * @param {number} bytes - Number of bytes
   * @returns {string} Hex encoded token
   */
  generateSecureToken(bytes = 32) {
    return crypto.randomBytes(bytes).toString('hex')
  }

  /**
   * Generate a UUID v4
   * @returns {string} UUID
   */
  generateUUID() {
    return crypto.randomUUID()
  }

  /**
   * Slugify a string (URL-friendly)
   * @param {string} text - Text to slugify
   * @returns {string} Slugified text
   */
  slugify(text) {
    return text
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '') // Remove accents
      .replace(/[^a-z0-9 -]/g, '') // Remove invalid chars
      .replace(/\s+/g, '-') // Replace spaces with -
      .replace(/-+/g, '-') // Replace multiple - with single -
      .trim('-') // Trim - from start and end
  }

  /**
   * Truncate text with ellipsis
   * @param {string} text - Text to truncate
   * @param {number} maxLength - Maximum length
   * @param {string} suffix - Suffix to add
   * @returns {string} Truncated text
   */
  truncate(text, maxLength = 100, suffix = '...') {
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength - suffix.length) + suffix
  }

  /**
   * Capitalize first letter of each word
   * @param {string} text - Text to capitalize
   * @returns {string} Capitalized text
   */
  capitalize(text) {
    return text.replace(/\b\w/g, l => l.toUpperCase())
  }

  /**
   * Clean text for speech synthesis
   * @param {string} text - Text to clean
   * @returns {string} Cleaned text
   */
  cleanTextForSpeech(text) {
    return text
      // Remove markdown links
      .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1')
      // Remove markdown bold/italic
      .replace(/\*\*([^*]+)\*\*/g, '$1')
      .replace(/\*([^*]+)\*/g, '$1')
      .replace(/__([^_]+)__/g, '$1')
      .replace(/_([^_]+)_/g, '$1')
      // Remove code blocks
      .replace(/`([^`]+)`/g, '$1')
      .replace(/```[\s\S]*?```/g, '')
      // Clean list markers
      .replace(/^[\s-*]+/gm, '')
      .replace(/^\s*\d+\.\s+/gm, '')
      // Remove quotes
      .replace(/^\s*>\s+/gm, '')
      // Remove headers
      .replace(/^#{1,6}\s+/gm, '')
      // Normalize whitespace
      .replace(/\s+/g, ' ')
      // Handle brand names for better pronunciation
      .replace(/Movistar\+/g, 'Movistar Plus')
      .trim()
  }

  // ===========================================
  // OBJECT UTILITIES
  // ===========================================

  /**
   * Deep clone an object
   * @param {*} obj - Object to clone
   * @returns {*} Cloned object
   */
  deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj
    if (obj instanceof Date) return new Date(obj.getTime())
    if (obj instanceof Array) return obj.map(item => this.deepClone(item))

    if (typeof obj === 'object') {
      const cloned = {}
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          cloned[key] = this.deepClone(obj[key])
        }
      }
      return cloned
    }
  }

  /**
   * Deep merge objects
   * @param {Object} target - Target object
   * @param {...Object} sources - Source objects
   * @returns {Object} Merged object
   */
  deepMerge(target, ...sources) {
    if (!sources.length) return target
    const source = sources.shift()

    if (this.isObject(target) && this.isObject(source)) {
      for (const key in source) {
        if (this.isObject(source[key])) {
          if (!target[key]) Object.assign(target, { [key]: {} })
          this.deepMerge(target[key], source[key])
        } else {
          Object.assign(target, { [key]: source[key] })
        }
      }
    }

    return this.deepMerge(target, ...sources)
  }

  /**
   * Check if value is a plain object
   * @param {*} item - Item to check
   * @returns {boolean} True if plain object
   */
  isObject(item) {
    return item && typeof item === 'object' && !Array.isArray(item)
  }

  /**
   * Pick specific properties from object
   * @param {Object} obj - Source object
   * @param {Array<string>} keys - Keys to pick
   * @returns {Object} Object with picked keys
   */
  pick(obj, keys) {
    const result = {}
    keys.forEach(key => {
      if (key in obj) {
        result[key] = obj[key]
      }
    })
    return result
  }

  /**
   * Omit specific properties from object
   * @param {Object} obj - Source object
   * @param {Array<string>} keys - Keys to omit
   * @returns {Object} Object without omitted keys
   */
  omit(obj, keys) {
    const result = { ...obj }
    keys.forEach(key => {
      delete result[key]
    })
    return result
  }

  /**
   * Get nested property safely
   * @param {Object} obj - Object to traverse
   * @param {string} path - Dot notation path
   * @param {*} defaultValue - Default value if not found
   * @returns {*} Property value or default
   */
  get(obj, path, defaultValue = undefined) {
    const keys = path.split('.')
    let result = obj

    for (const key of keys) {
      if (result?.[key] !== undefined) {
        result = result[key]
      } else {
        return defaultValue
      }
    }

    return result
  }

  /**
   * Set nested property safely
   * @param {Object} obj - Object to modify
   * @param {string} path - Dot notation path
   * @param {*} value - Value to set
   * @returns {Object} Modified object
   */
  set(obj, path, value) {
    const keys = path.split('.')
    const lastKey = keys.pop()
    let target = obj

    for (const key of keys) {
      if (!target[key] || typeof target[key] !== 'object') {
        target[key] = {}
      }
      target = target[key]
    }

    target[lastKey] = value
    return obj
  }

  // ===========================================
  // ARRAY UTILITIES
  // ===========================================

  /**
   * Remove duplicates from array
   * @param {Array} arr - Array to deduplicate
   * @param {string|Function} key - Key or function to compare by
   * @returns {Array} Deduplicated array
   */
  unique(arr, key = null) {
    if (!key) return [...new Set(arr)]

    const seen = new Set()
    return arr.filter(item => {
      const keyValue = typeof key === 'function' ? key(item) : item[key]
      if (seen.has(keyValue)) return false
      seen.add(keyValue)
      return true
    })
  }

  /**
   * Chunk array into smaller arrays
   * @param {Array} arr - Array to chunk
   * @param {number} size - Chunk size
   * @returns {Array<Array>} Chunked arrays
   */
  chunk(arr, size) {
    const chunks = []
    for (let i = 0; i < arr.length; i += size) {
      chunks.push(arr.slice(i, i + size))
    }
    return chunks
  }

  /**
   * Group array by key
   * @param {Array} arr - Array to group
   * @param {string|Function} key - Key or function to group by
   * @returns {Object} Grouped object
   */
  groupBy(arr, key) {
    return arr.reduce((groups, item) => {
      const groupKey = typeof key === 'function' ? key(item) : item[key]
      if (!groups[groupKey]) groups[groupKey] = []
      groups[groupKey].push(item)
      return groups
    }, {})
  }

  /**
   * Sort array of objects by key
   * @param {Array} arr - Array to sort
   * @param {string} key - Key to sort by
   * @param {string} direction - 'asc' or 'desc'
   * @returns {Array} Sorted array
   */
  sortBy(arr, key, direction = 'asc') {
    return arr.sort((a, b) => {
      const aVal = this.get(a, key)
      const bVal = this.get(b, key)

      if (aVal < bVal) return direction === 'asc' ? -1 : 1
      if (aVal > bVal) return direction === 'asc' ? 1 : -1
      return 0
    })
  }

  // ===========================================
  // ASYNC UTILITIES
  // ===========================================

  /**
   * Sleep for specified milliseconds
   * @param {number} ms - Milliseconds to sleep
   * @returns {Promise} Promise that resolves after delay
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * Retry an async operation
   * @param {Function} fn - Async function to retry
   * @param {number} maxRetries - Maximum retry attempts
   * @param {number} delay - Delay between retries in ms
   * @returns {Promise} Promise that resolves with result
   */
  async retry(fn, maxRetries = 3, delay = 1000) {
    let lastError

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await fn()
      } catch (error) {
        lastError = error

        if (attempt === maxRetries) break

        await this.sleep(delay * attempt) // Exponential backoff
      }
    }

    throw lastError
  }

  /**
   * Execute async operations with timeout
   * @param {Promise} promise - Promise to execute
   * @param {number} timeout - Timeout in ms
   * @returns {Promise} Promise that resolves or rejects with timeout
   */
  async withTimeout(promise, timeout) {
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error(`Operation timed out after ${timeout}ms`)), timeout)
    })

    return Promise.race([promise, timeoutPromise])
  }

  /**
   * Execute async operations in parallel with concurrency limit
   * @param {Array} items - Items to process
   * @param {Function} fn - Async function to execute
   * @param {number} concurrency - Max concurrent operations
   * @returns {Promise<Array>} Array of results
   */
  async parallel(items, fn, concurrency = 5) {
    const results = []
    const executing = []

    for (const item of items) {
      const promise = fn(item).then(result => {
        executing.splice(executing.indexOf(promise), 1)
        return result
      })

      results.push(promise)
      executing.push(promise)

      if (executing.length >= concurrency) {
        await Promise.race(executing)
      }
    }

    return Promise.all(results)
  }

  // ===========================================
  // VALIDATION UTILITIES
  // ===========================================

  /**
   * Check if value is empty
   * @param {*} value - Value to check
   * @returns {boolean} True if empty
   */
  isEmpty(value) {
    if (value == null) return true
    if (typeof value === 'string') return value.trim() === ''
    if (Array.isArray(value)) return value.length === 0
    if (typeof value === 'object') return Object.keys(value).length === 0
    return false
  }

  /**
   * Check if string is valid email
   * @param {string} email - Email to validate
   * @returns {boolean} True if valid email
   */
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  /**
   * Check if string is valid URL
   * @param {string} url - URL to validate
   * @returns {boolean} True if valid URL
   */
  isValidURL(url) {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  }

  /**
   * Check if value is valid JSON
   * @param {string} str - String to validate
   * @returns {boolean} True if valid JSON
   */
  isValidJSON(str) {
    try {
      JSON.parse(str)
      return true
    } catch {
      return false
    }
  }

  // ===========================================
  // FILE UTILITIES
  // ===========================================

  /**
   * Ensure directory exists
   * @param {string} dirPath - Directory path
   * @returns {Promise<void>}
   */
  async ensureDir(dirPath) {
    try {
      await fs.access(dirPath)
    } catch {
      await fs.mkdir(dirPath, { recursive: true })
    }
  }

  /**
   * Read JSON file safely
   * @param {string} filePath - File path
   * @param {*} defaultValue - Default value if file doesn't exist
   * @returns {Promise<*>} Parsed JSON or default value
   */
  async readJSON(filePath, defaultValue = {}) {
    try {
      const content = await fs.readFile(filePath, 'utf8')
      return JSON.parse(content)
    } catch {
      return defaultValue
    }
  }

  /**
   * Write JSON file safely
   * @param {string} filePath - File path
   * @param {*} data - Data to write
   * @param {Object} options - Write options
   * @returns {Promise<void>}
   */
  async writeJSON(filePath, data, options = {}) {
    const { indent = 2, ensureDir = true } = options

    if (ensureDir) {
      await this.ensureDir(path.dirname(filePath))
    }

    const content = JSON.stringify(data, null, indent)
    await fs.writeFile(filePath, content, 'utf8')
  }

  /**
   * Get file extension
   * @param {string} filename - Filename
   * @returns {string} File extension
   */
  getFileExtension(filename) {
    return path.extname(filename).toLowerCase().slice(1)
  }

  /**
   * Get file size in human readable format
   * @param {number} bytes - File size in bytes
   * @returns {string} Human readable size
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B'

    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // ===========================================
  // DATE UTILITIES
  // ===========================================

  /**
   * Format date relative to now
   * @param {Date|string} date - Date to format
   * @returns {string} Relative time string
   */
  formatRelativeTime(date) {
    const now = new Date()
    const past = new Date(date)
    const diffMs = now - past
    const diffSec = Math.floor(diffMs / 1000)
    const diffMin = Math.floor(diffSec / 60)
    const diffHour = Math.floor(diffMin / 60)
    const diffDay = Math.floor(diffHour / 24)

    if (diffSec < 60) return 'just now'
    if (diffMin < 60) return `${diffMin}m ago`
    if (diffHour < 24) return `${diffHour}h ago`
    if (diffDay < 7) return `${diffDay}d ago`

    return past.toLocaleDateString()
  }

  /**
   * Add time to date
   * @param {Date} date - Base date
   * @param {number} amount - Amount to add
   * @param {string} unit - Unit (ms, s, m, h, d)
   * @returns {Date} New date
   */
  addTime(date, amount, unit = 'ms') {
    const multipliers = {
      ms: 1,
      s: 1000,
      m: 60000,
      h: 3600000,
      d: 86400000
    }

    return new Date(date.getTime() + amount * multipliers[unit])
  }

  // ===========================================
  // CRYPTO UTILITIES
  // ===========================================

  /**
   * Hash string with SHA256
   * @param {string} str - String to hash
   * @returns {string} Hex encoded hash
   */
  hash(str) {
    return crypto.createHash('sha256').update(str).digest('hex')
  }

  /**
   * Create HMAC signature
   * @param {string} data - Data to sign
   * @param {string} secret - Secret key
   * @param {string} algorithm - Hash algorithm
   * @returns {string} Hex encoded signature
   */
  hmac(data, secret, algorithm = 'sha256') {
    return crypto.createHmac(algorithm, secret).update(data).digest('hex')
  }

  /**
   * Encrypt string with AES
   * @param {string} text - Text to encrypt
   * @param {string} key - Encryption key
   * @returns {string} Encrypted text with IV
   */
  encrypt(text, key) {
    const algorithm = 'aes-256-gcm'
    const keyBuffer = crypto.scryptSync(key, 'salt', 32)
    const iv = crypto.randomBytes(16)
    const cipher = crypto.createCipher(algorithm, keyBuffer, iv)

    let encrypted = cipher.update(text, 'utf8', 'hex')
    encrypted += cipher.final('hex')

    return iv.toString('hex') + ':' + encrypted
  }

  /**
   * Decrypt string with AES
   * @param {string} encryptedData - Encrypted data with IV
   * @param {string} key - Decryption key
   * @returns {string} Decrypted text
   */
  decrypt(encryptedData, key) {
    const algorithm = 'aes-256-gcm'
    const keyBuffer = crypto.scryptSync(key, 'salt', 32)
    const [ivHex, encrypted] = encryptedData.split(':')
    const iv = Buffer.from(ivHex, 'hex')
    const decipher = crypto.createDecipher(algorithm, keyBuffer, iv)

    let decrypted = decipher.update(encrypted, 'hex', 'utf8')
    decrypted += decipher.final('utf8')

    return decrypted
  }
}

// Create singleton instance
const helpers = new Helpers()

module.exports = { Helpers, helpers }
