/**
 * Type definitions and data structures for the voice AI system
 * Provides consistent data types across all components
 */

// ===========================================
// AUDIO TYPES
// ===========================================

/**
 * Audio configuration for speech recognition
 * @typedef {Object} AudioConfig
 * @property {string} encoding - Audio encoding format (LINEAR16, FLAC, etc.)
 * @property {number} sampleRateHertz - Sample rate in Hz (8000-48000)
 * @property {string} languageCode - Language code (es-ES, en-US, etc.)
 * @property {number} [channels=1] - Number of audio channels
 * @property {boolean} [enableNoiseCancellation=false] - Enable noise cancellation
 */

/**
 * Audio buffer with metadata
 * @typedef {Object} AudioBuffer
 * @property {Buffer|Uint8Array} data - Raw audio data
 * @property {string} format - Audio format
 * @property {number} sampleRate - Sample rate in Hz
 * @property {number} duration - Duration in milliseconds
 * @property {number} size - Buffer size in bytes
 * @property {Date} timestamp - When the audio was captured
 */

/**
 * Speech recognition result
 * @typedef {Object} SpeechResult
 * @property {string} text - Transcribed text
 * @property {boolean} isFinal - Whether this is a final result
 * @property {number} confidence - Confidence score (0-1)
 * @property {string} provider - Provider that generated the result
 * @property {number} [duration] - Audio duration in milliseconds
 * @property {number} [offset] - Offset from start of stream
 * @property {Array<Word>} [words] - Word-level timing information
 */

/**
 * Word-level timing information
 * @typedef {Object} Word
 * @property {string} word - The word text
 * @property {number} startTime - Start time in milliseconds
 * @property {number} endTime - End time in milliseconds
 * @property {number} confidence - Word confidence score
 */

// ===========================================
// TEXT-TO-SPEECH TYPES
// ===========================================

/**
 * TTS voice configuration
 * @typedef {Object} VoiceConfig
 * @property {string} voiceId - Voice identifier
 * @property {string} language - Language code
 * @property {string} gender - Voice gender (male/female/neutral)
 * @property {string} provider - TTS provider (google/azure)
 * @property {string} [style] - Voice style (casual/formal/etc)
 * @property {number} [speed=1.0] - Speaking speed multiplier
 * @property {number} [pitch=0] - Pitch adjustment (-50 to +50)
 * @property {number} [volume=0] - Volume adjustment (-50 to +50)
 */

/**
 * TTS generation request
 * @typedef {Object} TTSRequest
 * @property {string} text - Text to synthesize
 * @property {VoiceConfig} voice - Voice configuration
 * @property {string} [format='mp3'] - Output audio format
 * @property {Object} [ssml] - SSML configuration
 * @property {boolean} [cache=true] - Whether to cache result
 */

/**
 * TTS generation result
 * @typedef {Object} TTSResult
 * @property {Buffer} audioData - Generated audio data
 * @property {string} format - Audio format
 * @property {number} duration - Audio duration in milliseconds
 * @property {number} size - Audio size in bytes
 * @property {string} provider - TTS provider used
 * @property {boolean} fromCache - Whether result came from cache
 */

// ===========================================
// AI CONVERSATION TYPES
// ===========================================

/**
 * Conversation turn
 * @typedef {Object} ConversationTurn
 * @property {boolean} user - Whether this turn is from user
 * @property {string} content - Turn content
 * @property {Date} timestamp - When the turn occurred
 * @property {string} [id] - Unique turn identifier
 * @property {Object} [metadata] - Additional metadata
 */

/**
 * AI generation request
 * @typedef {Object} AIRequest
 * @property {Array<ConversationTurn>} history - Conversation history
 * @property {string} [systemPrompt] - System prompt override
 * @property {number} [maxTokens=500] - Maximum tokens to generate
 * @property {number} [temperature=0.7] - Generation temperature
 * @property {string} [provider] - AI provider to use
 */

/**
 * AI generation result
 * @typedef {Object} AIResult
 * @property {string} text - Generated text
 * @property {string} provider - AI provider used
 * @property {Object} usage - Token usage statistics
 * @property {number} responseTime - Generation time in milliseconds
 * @property {string} model - Model used for generation
 * @property {boolean} cached - Whether result was cached
 */

/**
 * Token usage statistics
 * @typedef {Object} TokenUsage
 * @property {number} promptTokens - Tokens in prompt
 * @property {number} completionTokens - Tokens in completion
 * @property {number} totalTokens - Total tokens used
 * @property {number} [cost] - Estimated cost in USD
 */

// ===========================================
// SESSION & WEBSOCKET TYPES
// ===========================================

/**
 * Voice session configuration
 * @typedef {Object} SessionConfig
 * @property {string} sessionId - Unique session identifier
 * @property {string} provider - Service provider (google/azure)
 * @property {AudioConfig} audioConfig - Audio configuration
 * @property {VoiceConfig} voiceConfig - TTS voice configuration
 * @property {Object} aiConfig - AI configuration
 * @property {boolean} [enableCaching=true] - Enable response caching
 * @property {number} [timeout=30000] - Session timeout in milliseconds
 */

/**
 * Voice session state
 * @typedef {Object} SessionState
 * @property {string} sessionId - Session identifier
 * @property {boolean} isActive - Whether session is active
 * @property {boolean} isRecording - Whether currently recording
 * @property {boolean} isProcessing - Whether processing audio/AI
 * @property {Date} startTime - Session start time
 * @property {Date} lastActivity - Last activity timestamp
 * @property {Array<ConversationTurn>} history - Conversation history
 * @property {Object} statistics - Session statistics
 */

/**
 * WebSocket message types
 * @typedef {Object} SocketMessage
 * @property {string} type - Message type
 * @property {Object} data - Message data
 * @property {string} [id] - Message identifier
 * @property {Date} timestamp - Message timestamp
 */

/**
 * Socket event types
 * @enum {string}
 */
const SOCKET_EVENTS = {
  // Client to server
  AUDIO_DATA: 'audio',
  START_SESSION: 'start-session',
  END_SESSION: 'end-session',
  MUTE_SOUND: 'mute-sound',
  UNMUTE_SOUND: 'unmute-sound',
  PING: 'ping',

  // Server to client
  TRANSCRIPTION: 'transcription',
  REPLY: 'reply',
  LOADING: 'loading',
  ERROR: 'error',
  SESSION_STATUS: 'session-status',
  PONG: 'pong'
}

// ===========================================
// PROVIDER TYPES
// ===========================================

/**
 * Service provider configuration
 * @typedef {Object} ProviderConfig
 * @property {string} name - Provider name (google/azure)
 * @property {boolean} enabled - Whether provider is enabled
 * @property {Object} speech - Speech service configuration
 * @property {Object} ai - AI service configuration
 * @property {Object} tts - TTS service configuration
 * @property {number} priority - Provider priority (lower = higher priority)
 */

/**
 * Provider health status
 * @typedef {Object} ProviderHealth
 * @property {string} provider - Provider name
 * @property {boolean} healthy - Overall health status
 * @property {Object} services - Individual service health
 * @property {number} responseTime - Average response time
 * @property {Date} lastCheck - Last health check time
 * @property {string} [error] - Error message if unhealthy
 */

/**
 * Service status
 * @typedef {Object} ServiceStatus
 * @property {boolean} initialized - Whether service is initialized
 * @property {boolean} healthy - Whether service is healthy
 * @property {string} [error] - Error message if unhealthy
 * @property {Object} config - Service configuration
 * @property {Object} [statistics] - Service usage statistics
 */

// ===========================================
// ERROR TYPES
// ===========================================

/**
 * Voice AI error
 * @typedef {Object} VoiceError
 * @property {string} code - Error code
 * @property {string} message - Error message
 * @property {string} type - Error type (validation/network/service/etc)
 * @property {string} [provider] - Provider that caused error
 * @property {Object} [details] - Additional error details
 * @property {Date} timestamp - When error occurred
 * @property {boolean} recoverable - Whether error is recoverable
 */

/**
 * Error codes
 * @enum {string}
 */
const ERROR_CODES = {
  // Validation errors
  INVALID_AUDIO_BUFFER: 'INVALID_AUDIO_BUFFER',
  INVALID_TEXT_INPUT: 'INVALID_TEXT_INPUT',
  INVALID_API_KEY: 'INVALID_API_KEY',
  INVALID_CONFIGURATION: 'INVALID_CONFIGURATION',

  // Service errors
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  SERVICE_TIMEOUT: 'SERVICE_TIMEOUT',
  SERVICE_QUOTA_EXCEEDED: 'SERVICE_QUOTA_EXCEEDED',
  SERVICE_AUTHENTICATION_FAILED: 'SERVICE_AUTHENTICATION_FAILED',

  // Network errors
  NETWORK_ERROR: 'NETWORK_ERROR',
  CONNECTION_FAILED: 'CONNECTION_FAILED',

  // Session errors
  SESSION_NOT_FOUND: 'SESSION_NOT_FOUND',
  SESSION_EXPIRED: 'SESSION_EXPIRED',
  SESSION_LIMIT_EXCEEDED: 'SESSION_LIMIT_EXCEEDED',

  // Rate limiting
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  SOCKET_RATE_LIMIT: 'SOCKET_RATE_LIMIT'
}

// ===========================================
// STATISTICS TYPES
// ===========================================

/**
 * Service usage statistics
 * @typedef {Object} ServiceStats
 * @property {number} totalRequests - Total requests processed
 * @property {number} successfulRequests - Successful requests
 * @property {number} failedRequests - Failed requests
 * @property {number} averageResponseTime - Average response time in ms
 * @property {number} totalProcessingTime - Total processing time in ms
 * @property {Object} errorCounts - Error counts by type
 * @property {Date} periodStart - Statistics period start
 * @property {Date} lastUpdated - Last update time
 */

/**
 * Performance metrics
 * @typedef {Object} PerformanceMetrics
 * @property {number} audioProcessingTime - Audio processing time in ms
 * @property {number} speechRecognitionTime - Speech recognition time in ms
 * @property {number} aiGenerationTime - AI generation time in ms
 * @property {number} ttsGenerationTime - TTS generation time in ms
 * @property {number} totalLatency - Total end-to-end latency in ms
 * @property {number} memoryUsage - Memory usage in MB
 * @property {number} cpuUsage - CPU usage percentage
 */

// ===========================================
// CONFIGURATION TYPES
// ===========================================

/**
 * Application configuration
 * @typedef {Object} AppConfig
 * @property {Object} server - Server configuration
 * @property {Object} security - Security configuration
 * @property {Object} providers - Provider configurations
 * @property {Object} ai - AI configuration
 * @property {Object} performance - Performance settings
 * @property {Object} monitoring - Monitoring settings
 */

/**
 * Environment configuration
 * @typedef {Object} EnvironmentConfig
 * @property {string} nodeEnv - Node environment (development/production/test)
 * @property {boolean} isDevelopment - Whether in development mode
 * @property {boolean} isProduction - Whether in production mode
 * @property {boolean} isTest - Whether in test mode
 * @property {number} port - Server port
 * @property {string} logLevel - Logging level
 */

// ===========================================
// UTILITY TYPES
// ===========================================

/**
 * Validation result
 * @typedef {Object} ValidationResult
 * @property {boolean} valid - Whether validation passed
 * @property {Array<string>} errors - Validation errors
 * @property {Array<string>} [warnings] - Validation warnings
 * @property {Object} [metadata] - Additional validation metadata
 * @property {*} [sanitized] - Sanitized/cleaned value
 */

/**
 * API response wrapper
 * @typedef {Object} ApiResponse
 * @property {boolean} success - Whether request was successful
 * @property {*} data - Response data
 * @property {string} [error] - Error message if failed
 * @property {string} [code] - Error code if failed
 * @property {Object} [metadata] - Additional response metadata
 * @property {Date} timestamp - Response timestamp
 */

/**
 * Pagination information
 * @typedef {Object} Pagination
 * @property {number} page - Current page number
 * @property {number} limit - Items per page
 * @property {number} total - Total number of items
 * @property {number} pages - Total number of pages
 * @property {boolean} hasNext - Whether there are more pages
 * @property {boolean} hasPrev - Whether there are previous pages
 */

// ===========================================
// EXPORTS
// ===========================================

module.exports = {
  // Enums
  SOCKET_EVENTS,
  ERROR_CODES,

  // Type checking utilities
  isValidAudioConfig: (config) => {
    return config &&
           typeof config.encoding === 'string' &&
           typeof config.sampleRateHertz === 'number' &&
           typeof config.languageCode === 'string'
  },

  isValidVoiceConfig: (config) => {
    return config &&
           typeof config.voiceId === 'string' &&
           typeof config.language === 'string' &&
           typeof config.provider === 'string'
  },

  isValidConversationTurn: (turn) => {
    return turn &&
           typeof turn.user === 'boolean' &&
           typeof turn.content === 'string' &&
           turn.content.trim().length > 0
  },

  isValidSessionConfig: (config) => {
    return config &&
           typeof config.sessionId === 'string' &&
           typeof config.provider === 'string' &&
           config.audioConfig &&
           config.voiceConfig
  },

  // Factory functions for creating typed objects
  createAudioConfig: (encoding, sampleRate, language, options = {}) => ({
    encoding,
    sampleRateHertz: sampleRate,
    languageCode: language,
    channels: options.channels || 1,
    enableNoiseCancellation: options.enableNoiseCancellation || false,
    ...options
  }),

  createVoiceConfig: (voiceId, language, provider, options = {}) => ({
    voiceId,
    language,
    provider,
    gender: options.gender || 'neutral',
    speed: options.speed || 1.0,
    pitch: options.pitch || 0,
    volume: options.volume || 0,
    ...options
  }),

  createConversationTurn: (isUser, content, options = {}) => ({
    user: isUser,
    content: content.trim(),
    timestamp: new Date(),
    id: options.id || `turn_${Date.now()}_${Math.random().toString(36).substring(2)}`,
    ...options
  }),

  createSessionConfig: (sessionId, provider, audioConfig, voiceConfig, options = {}) => ({
    sessionId,
    provider,
    audioConfig,
    voiceConfig,
    aiConfig: options.aiConfig || {},
    enableCaching: options.enableCaching !== false,
    timeout: options.timeout || 30000,
    ...options
  }),

  createApiResponse: (success, data, error = null, options = {}) => ({
    success,
    data: success ? data : null,
    error: success ? null : error,
    code: options.code || null,
    metadata: options.metadata || null,
    timestamp: new Date(),
    ...options
  }),

  createVoiceError: (code, message, type, options = {}) => ({
    code,
    message,
    type,
    provider: options.provider || null,
    details: options.details || null,
    timestamp: new Date(),
    recoverable: options.recoverable !== false,
    ...options
  })
}
