.
├── azure
│   ├── azureOpenAIService.js
│   ├── azureSpeechService.js
│   ├── azureTTSService.js
│   └── config.js
├── core
│   ├── config
│   │   ├── environment.js
│   │   └── security.js
│   ├── index.js
│   ├── types
│   │   └── voice.js
│   └── utils
│       ├── helpers.js
│       ├── index.js
│       ├── logger.js
│       ├── performance.js
│       ├── textProcessor.js
│       └── validation.js
├── docs
│   ├── azure_readme.md
│   └── file-system.md
├── .env
├── .gitignore
├── index.js
├── modules
│   ├── AIService.js
│   ├── config.js
│   ├── security.js
│   └── voiceService.js
├── package.json
├── package-lock.json
├── .prettierrc.cjs
├── README.md
├── services
│   ├── factory
│   │   ├── ConfigManager.js
│   │   └── ServiceFactory.js
│   ├── interfaces
│   │   ├── IAIService.js
│   │   ├── ISpeechService.js
│   │   └── ITTSService.js
│   └── providers
│       ├── azure
│       │   ├── AzureAIService.js
│       │   ├── AzureSpeechService.js
│       │   └── AzureTTSService.js
│       └── google
│           ├── GoogleAIService.js
│           ├── GoogleSpeechService.js
│           └── GoogleTTSService.js
└── yarn.lock

14 directories, 39 files
