const axios = require('axios')

const axiosInstance = axios.create({
  baseURL: process.env.AUDIO_BACKEND_BASE_URL,
  headers: {
    Authorization: process.env.AUDIO_BACKEND_API_KEY
  }
})

const generateSpeech = async (text) => {
  // Validate input
  if (typeof text !== 'string') {
    throw new Error('Text input must be a string')
  }

  if (text.trim() === '') {
    throw new Error('Text input cannot be empty')
  }

  // Validate text length (reasonable limit for TTS)
  const maxTextLength = 5000
  if (text.length > maxTextLength) {
    throw new Error(`Text too long for speech generation: ${text.length} characters (max: ${maxTextLength})`)
  }

  // Validate environment variables
  if (!process.env.AUDIO_BACKEND_BASE_URL) {
    throw new Error('AUDIO_BACKEND_BASE_URL environment variable not set')
  }

  if (!process.env.AUDIO_BACKEND_API_KEY) {
    throw new Error('AUDIO_BACKEND_API_KEY environment variable not set')
  }

  try {
    const cleanedText = text.replaceAll('Movistar+', 'Movistar Plus')

    const response = await axiosInstance.post(
      '/t2s',
      {
        input_text: cleanedText,
        voice_params: {
          voice_id: 'Ximena'
        },
        output_format: 'mp3'
      },
      {
        responseType: 'arraybuffer',
        timeout: 30000, // 30 second timeout
        maxContentLength: 10 * 1024 * 1024 // 10MB max response size
      }
    )

    // Validate response
    if (!response.data) {
      throw new Error('Empty response from speech service')
    }

    if (response.data.byteLength === 0) {
      throw new Error('Speech service returned empty audio data')
    }

    return Buffer.from(response.data, 'binary')
  } catch (error) {
    console.error('Error generating speech with Speech Server:', error.message || error)

    // Provide more specific error messages based on error type
    if (error.code === 'ECONNREFUSED') {
      throw new Error('Speech service is unavailable. Please try again later.')
    } else if (error.code === 'ETIMEDOUT' || error.message.includes('timeout')) {
      throw new Error('Speech generation timed out. Please try again with shorter text.')
    } else if (error.response) {
      const status = error.response.status
      if (status === 401) {
        throw new Error('Speech service authentication failed. Please check API key.')
      } else if (status === 429) {
        throw new Error('Speech service rate limit exceeded. Please try again later.')
      } else if (status >= 500) {
        throw new Error('Speech service is experiencing issues. Please try again later.')
      } else {
        throw new Error(`Speech service error (${status}): ${error.response.statusText || 'Unknown error'}`)
      }
    } else {
      throw new Error(`Speech generation failed: ${error.message || 'Unknown error occurred'}`)
    }
  }
}

module.exports = { generateSpeech }
