const { VertexAI } = require('@google-cloud/vertexai')
const config = require('./config')

let model

const createAIService = () => {
  const vertexAI = new VertexAI({
    project: 'sandbox-innovacion-3',
    location: 'europe-southwest1'
  })
  model = vertexAI.getGenerativeModel({
    model: 'gemini-2.0-flash-001',
    systemInstruction: {
      role: 'system',
      parts: [{ text: config.AI_PROMPT }]
    }
  })
}

const generateAIReply = async (conversationHistory) => {
  // Validate input
  if (!Array.isArray(conversationHistory)) {
    throw new Error('Conversation history must be an array')
  }

  if (conversationHistory.length === 0) {
    throw new Error('Conversation history cannot be empty')
  }

  // Validate model is initialized
  if (!model) {
    throw new Error('AI model not initialized. Call createAIService() first.')
  }

  try {
    const formattedHistory = conversationHistory.map((turn, index) => {
      if (!turn || typeof turn !== 'object') {
        throw new Error(`Invalid conversation turn at index ${index}`)
      }

      if (typeof turn.content !== 'string' || turn.content.trim() === '') {
        throw new Error(`Invalid or empty content at conversation turn ${index}`)
      }

      return {
        role: turn.user ? 'user' : 'model',
        parts: [{ text: turn.content.trim() }]
      }
    })

    const result = await model.generateContent({
      contents: formattedHistory
    })

    // Validate response structure
    if (!result || !result.response) {
      throw new Error('Invalid response structure from AI model')
    }

    if (!result.response.candidates || result.response.candidates.length === 0) {
      throw new Error('No candidates in AI response')
    }

    const candidate = result.response.candidates[0]
    if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {
      throw new Error('Invalid candidate structure in AI response')
    }

    const responseText = candidate.content.parts[0].text
    if (typeof responseText !== 'string') {
      throw new Error('AI response text is not a string')
    }

    return responseText.trim()
  } catch (error) {
    console.error('Error generating AI reply:', error.message || error)

    // Re-throw with more context for different error types
    if (error.message && error.message.includes('quota')) {
      throw new Error('AI service quota exceeded. Please try again later.')
    } else if (error.message && error.message.includes('network')) {
      throw new Error('Network error connecting to AI service. Please check your connection.')
    } else if (error.message && error.message.includes('authentication')) {
      throw new Error('AI service authentication failed. Please check configuration.')
    } else {
      throw new Error(`AI service error: ${error.message || 'Unknown error occurred'}`)
    }
  }
}

module.exports = {
  createAIService,
  generateAIReply
}
