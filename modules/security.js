const cors = require('cors')
const { customLog } = require('./logger')

const validateApiKey = (req, res, next) => {
  const apiKey = req.headers['x-api-key']
  if (!apiKey || apiKey !== process.env.API_KEY) {
    customLog('Unauthorized API access attempt:', req.ip)
    return res.status(401).json({ error: 'Unauthorized access' })
  }
  next()
}

const socketAuth = (socket, next) => {
  const apiKey = socket.handshake.auth.apiKey
  if (!apiKey || apiKey !== process.env.API_KEY) {
    customLog('Unauthorized socket connection attempt:', socket.handshake.address)
    return next(new Error('Authentication error'))
  }
  socket.user = { authenticated: true, connectionTime: new Date() }
  next()
}

const monitorSocketActivity = (socket) => {
  let messageCount = 0
  const messageThreshold = 1000 // 1000 messages per minute
  const interval = 60000 // 1 minute

  const timer = setInterval(() => {
    messageCount = 0
  }, interval)

  socket.on('audio', () => {
    messageCount++
    if (messageCount > messageThreshold) {
      customLog('Rate limit exceeded for socket:', socket.id)
      socket.emit('error', 'Rate limit exceeded')
      socket.disconnect()
    }
  })

  socket.on('disconnect', () => {
    clearInterval(timer)
  })
}

const configureCors = (app) => {
  const allowedOrigins =
    process.env.NODE_ENV === 'production'
      ? [process.env.FRONTEND_DEV_URL, process.env.FRONTEND_PRO_URL]
      : ['http://localhost:3000', 'http://localhost:5173']

  const corsOptions = {
    origin: (origin, callback) => {
      if (!origin || allowedOrigins.includes(origin) || allowedOrigins.includes('*')) {
        callback(null, true)
      } else {
        customLog('CORS blocked request from:', origin)
        callback(new Error('Not allowed by CORS'))
      }
    },
    methods: ['GET', 'POST'],
    credentials: true
  }

  app.use(cors(corsOptions))
}

const configureSocketSecurity = (io) => {
  io.use(socketAuth)

  io.engine.on('connection_error', (err) => {
    customLog('Socket.IO connection error:', err)
  })

  io.on('connection', (socket) => {
    monitorSocketActivity(socket)

    let idleTimeout = setTimeout(() => {
      customLog('Closing idle socket:', socket.id)
      socket.disconnect()
    }, 30 * 60 * 1000) // 30 minutes

    socket.on('audio', () => {
      clearTimeout(idleTimeout)
      idleTimeout = setTimeout(() => {
        customLog('Closing idle socket:', socket.id)
        socket.disconnect()
      }, 30 * 60 * 1000)
    })

    socket.on('disconnect', () => {
      clearTimeout(idleTimeout)
    })
  })
}

const setSecurityHeaders = (app) => {
  app.use((req, res, next) => {
    res.setHeader('X-Content-Type-Options', 'nosniff')
    res.setHeader('X-Frame-Options', 'DENY')
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains')
    res.setHeader(
      'Content-Security-Policy',
      "default-src 'self'; connect-src 'self' wss: ws:; img-src 'self' data:; style-src 'self'; font-src 'self'; object-src 'none'; frame-ancestors 'none'"
    )

    next()
  })
}

module.exports = {
  validateApiKey,
  socketAuth,
  configureCors,
  configureSocketSecurity,
  setSecurityHeaders
}
