/**
 * Interface para servicios de IA/LLM
 * Define los métodos que deben implementar todos los proveedores de IA
 */
class IAIService {
  /**
   * Inicializa el servicio de IA
   * @throws {Error} Si la inicialización falla
   */
  init() {
    throw new Error('Method init() must be implemented')
  }

  /**
   * Genera una respuesta basada en el historial de conversación
   * @param {Array} conversationHistory - Array de objetos {user: boolean, content: string}
   * @returns {Promise<string>} Respuesta generada por la IA
   * @throws {Error} Si la generación falla
   */
  async generateReply(conversationHistory) {
    throw new Error('Method generateReply() must be implemented')
  }

  /**
   * Prueba la conexión con el servicio de IA
   * @returns {Promise<boolean>} True si la conexión es exitosa
   */
  async testConnection() {
    throw new Error('Method testConnection() must be implemented')
  }

  /**
   * Obtiene el estado del servicio
   * @returns {Object} Estado actual del servicio
   */
  getStatus() {
    throw new Error('Method getStatus() must be implemented')
  }

  /**
   * Obtiene información sobre el modelo actual
   * @returns {Object} Información del modelo
   */
  getModelInfo() {
    throw new Error('Method getModelInfo() must be implemented')
  }

  /**
   * Valida el historial de conversación
   * @param {Array} conversationHistory - Historial a validar
   * @returns {Array} Historial validado y limpio
   * @throws {Error} Si el historial no es válido
   */
  validateConversationHistory(conversationHistory) {
    if (!Array.isArray(conversationHistory)) {
      throw new Error('Conversation history must be an array')
    }

    if (conversationHistory.length === 0) {
      throw new Error('Conversation history cannot be empty')
    }

    // Limit conversation history to prevent memory/token issues
    const maxHistoryLength = 50
    let validatedHistory = conversationHistory

    if (conversationHistory.length > maxHistoryLength) {
      console.log(`Warning: Trimming conversation history from ${conversationHistory.length} to ${maxHistoryLength} entries`)
      validatedHistory = conversationHistory.slice(-maxHistoryLength)
    }

    // Validate each turn in the conversation
    validatedHistory.forEach((turn, index) => {
      if (!turn || typeof turn !== 'object') {
        throw new Error(`Invalid conversation turn at index ${index}`)
      }

      if (typeof turn.content !== 'string' || turn.content.trim() === '') {
        throw new Error(`Invalid or empty content at conversation turn ${index}`)
      }

      if (typeof turn.user !== 'boolean') {
        throw new Error(`Invalid user flag at conversation turn ${index}`)
      }
    })

    return validatedHistory
  }

  /**
   * Valida y limpia el texto de entrada del usuario
   * @param {string} text - Texto a validar
   * @param {number} maxLength - Longitud máxima permitida
   * @returns {string} Texto validado y limpio
   * @throws {Error} Si el texto no es válido
   */
  validateUserInput(text, maxLength = 1000) {
    if (typeof text !== 'string') {
      throw new Error('User input must be a string')
    }

    if (text.trim() === '') {
      throw new Error('User input cannot be empty')
    }

    if (text.length > maxLength) {
      console.log(`Warning: Truncating long user input from ${text.length} to ${maxLength} characters`)
      return text.substring(0, maxLength)
    }

    return text.trim()
  }

  /**
   * Maneja errores específicos del proveedor y los normaliza
   * @param {Error} error - Error original
   * @returns {Error} Error normalizado
   */
  handleProviderError(error) {
    const errorMessage = error.message || error.toString()

    if (errorMessage.includes('quota') || errorMessage.includes('limit')) {
      return new Error('AI service quota exceeded. Please try again later.')
    } else if (errorMessage.includes('authentication') || errorMessage.includes('unauthorized')) {
      return new Error('AI service authentication failed. Please check configuration.')
    } else if (errorMessage.includes('network') || errorMessage.includes('connection')) {
      return new Error('Network error connecting to AI service. Please check your connection.')
    } else if (errorMessage.includes('timeout')) {
      return new Error('AI service request timed out. Please try again.')
    } else {
      return new Error(`AI service error: ${errorMessage}`)
    }
  }
}

module.exports = { IAIService }
