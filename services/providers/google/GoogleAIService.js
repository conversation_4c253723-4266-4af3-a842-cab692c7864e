const { VertexAI } = require('@google-cloud/vertexai')
const { IAIService } = require('../../interfaces/IAIService')
const { customLog } = require('../../../core/utils/logger')

class GoogleAIService extends IAIService {
  constructor(config) {
    super()
    this.config = config || {}
    this.model = null
    this.vertexAI = null

    this.init()
  }

  init() {
    try {
      // Validate configuration
      if (!this.config.project) {
        throw new Error('Google Cloud project not configured')
      }

      if (!this.config.location) {
        throw new Error('Google Cloud location not configured')
      }

      // Initialize Vertex AI
      this.vertexAI = new VertexAI({
        project: this.config.project,
        location: this.config.location
      })

      // Initialize the model
      this.model = this.vertexAI.getGenerativeModel({
        model: this.config.model || 'gemini-2.0-flash-001',
        systemInstruction: {
          role: 'system',
          parts: [{ text: this.config.systemPrompt || 'You are a helpful assistant.' }]
        },
        generationConfig: {
          maxOutputTokens: this.config.maxTokens || 500,
          temperature: this.config.temperature || 0.7,
          topP: this.config.topP || 0.9
        }
      })

      customLog('Google AI Service (Vertex AI) initialized successfully')

    } catch (error) {
      customLog('Error initializing Google AI Service:', error.message)
      throw error
    }
  }

  async generateReply(conversationHistory) {
    // Use interface validation
    const validatedHistory = this.validateConversationHistory(conversationHistory)

    // Validate model is initialized
    if (!this.model) {
      throw new Error('AI model not initialized. Call init() first.')
    }

    try {
      // Format conversation history for Vertex AI format
      const formattedHistory = validatedHistory.map(turn => ({
        role: turn.user ? 'user' : 'model',
        parts: [{ text: turn.content.trim() }]
      }))

      customLog('Sending request to Google Vertex AI with', formattedHistory.length, 'messages')

      const result = await this.model.generateContent({
        contents: formattedHistory
      })

      // Validate response structure
      if (!result || !result.response) {
        throw new Error('Invalid response structure from AI model')
      }

      if (!result.response.candidates || result.response.candidates.length === 0) {
        throw new Error('No candidates in AI response')
      }

      const candidate = result.response.candidates[0]
      if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {
        throw new Error('Invalid candidate structure in AI response')
      }

      const responseText = candidate.content.parts[0].text
      if (typeof responseText !== 'string') {
        throw new Error('AI response text is not a string')
      }

      // Log usage information if available
      if (result.response.usageMetadata) {
        customLog('Google AI usage:', {
          promptTokenCount: result.response.usageMetadata.promptTokenCount,
          candidatesTokenCount: result.response.usageMetadata.candidatesTokenCount,
          totalTokenCount: result.response.usageMetadata.totalTokenCount,
          provider: 'google'
        })
      }

      return responseText.trim()

    } catch (error) {
      customLog('Error generating Google AI reply:', error.message || error)

      // Use interface error handling
      throw this.handleProviderError(error)
    }
  }

  async testConnection() {
    try {
      if (!this.model) {
        throw new Error('Model not initialized')
      }

      const testResult = await this.model.generateContent({
        contents: [
          {
            role: 'user',
            parts: [{ text: 'Say hello' }]
          }
        ]
      })

      if (testResult && testResult.response && testResult.response.candidates) {
        customLog('Google AI connection test successful')
        return true
      }

      return false

    } catch (error) {
      customLog('Google AI connection test failed:', error.message)
      return false
    }
  }

  getStatus() {
    return {
      provider: 'google',
      service: 'vertex-ai',
      isInitialized: this.model !== null,
      project: this.config.project,
      location: this.config.location,
      model: this.config.model || 'gemini-2.0-flash-001'
    }
  }

  getModelInfo() {
    return {
      provider: 'google',
      service: 'vertex-ai',
      model: this.config.model || 'gemini-2.0-flash-001',
      project: this.config.project,
      location: this.config.location,
      maxTokens: this.config.maxTokens || 500,
      temperature: this.config.temperature || 0.7,
      topP: this.config.topP || 0.9,
      features: [
        'chat_completions',
        'system_instructions',
        'conversation_history',
        'token_usage_tracking',
        'multimodal_support'
      ]
    }
  }

  // Override interface error handling for Google-specific errors
  handleProviderError(error) {
    const errorMessage = error.message || error.toString()

    // Google-specific error handling
    if (errorMessage.includes('quota') || errorMessage.includes('QUOTA_EXCEEDED')) {
      return new Error('Google AI service quota exceeded. Please try again later.')
    } else if (errorMessage.includes('PERMISSION_DENIED') || errorMessage.includes('authentication')) {
      return new Error('Google AI service authentication failed. Please check configuration.')
    } else if (errorMessage.includes('INVALID_ARGUMENT')) {
      return new Error('Invalid request parameters for Google AI service.')
    } else if (errorMessage.includes('RESOURCE_EXHAUSTED')) {
      return new Error('Google AI service temporarily overwhelmed. Please try again later.')
    } else if (errorMessage.includes('DEADLINE_EXCEEDED') || errorMessage.includes('timeout')) {
      return new Error('Google AI service request timed out. Please try again.')
    }

    // Fall back to base interface error handling
    return super.handleProviderError(error)
  }

  // Google-specific configuration method
  updateConfiguration(newConfig) {
    const updatedConfig = { ...this.config, ...newConfig }

    // Only reinitialize if critical config changed
    const criticalFields = ['project', 'location', 'model']
    const shouldReinitialize = criticalFields.some(field =>
      this.config[field] !== updatedConfig[field]
    )

    this.config = updatedConfig

    if (shouldReinitialize) {
      customLog('Critical configuration changed, reinitializing Google AI model')
      this.init()
    }

    return this.getStatus()
  }

  // Method to get available models
  getAvailableModels() {
    return [
      {
        name: 'gemini-2.0-flash-001',
        description: 'Latest Gemini 2.0 Flash model',
        provider: 'google',
        features: ['text', 'multimodal', 'fast']
      },
      {
        name: 'gemini-1.5-pro',
        description: 'Gemini 1.5 Pro model',
        provider: 'google',
        features: ['text', 'multimodal', 'long-context']
      },
      {
        name: 'gemini-1.5-flash',
        description: 'Gemini 1.5 Flash model',
        provider: 'google',
        features: ['text', 'multimodal', 'fast']
      }
    ]
  }

  // Method to count tokens (useful for cost estimation)
  async countTokens(text) {
    try {
      if (!this.model) {
        throw new Error('Model not initialized')
      }

      const request = {
        contents: [
          {
            role: 'user',
            parts: [{ text: text }]
          }
        ]
      }

      const result = await this.model.countTokens(request)
      return result.totalTokens || 0

    } catch (error) {
      customLog('Error counting tokens:', error.message)
      // Fallback to rough estimation
      return Math.ceil(text.length / 4)
    }
  }

  // Method for streaming responses (advanced feature)
  async generateStreamingReply(conversationHistory, onChunk) {
    const validatedHistory = this.validateConversationHistory(conversationHistory)

    if (!this.model) {
      throw new Error('AI model not initialized')
    }

    try {
      const formattedHistory = validatedHistory.map(turn => ({
        role: turn.user ? 'user' : 'model',
        parts: [{ text: turn.content.trim() }]
      }))

      const result = await this.model.generateContentStream({
        contents: formattedHistory
      })

      let fullResponse = ''

      for await (const chunk of result.stream) {
        if (chunk.candidates && chunk.candidates[0] && chunk.candidates[0].content) {
          const chunkText = chunk.candidates[0].content.parts[0]?.text || ''
          fullResponse += chunkText

          if (onChunk && chunkText) {
            onChunk(chunkText)
          }
        }
      }

      return fullResponse.trim()

    } catch (error) {
      customLog('Error in streaming response:', error.message)
      throw this.handleProviderError(error)
    }
  }

  // Method to validate model availability
  async validateModel() {
    try {
      const models = this.getAvailableModels()
      const currentModel = this.config.model || 'gemini-2.0-flash-001'

      const isSupported = models.some(model => model.name === currentModel)

      if (!isSupported) {
        customLog(`Warning: Model ${currentModel} may not be supported`)
      }

      return isSupported
    } catch (error) {
      customLog('Error validating model:', error.message)
      return false
    }
  }
}

module.exports = { GoogleAIService }
