const axios = require('axios')
const { ITTSService } = require('../../interfaces/ITTSService')
const { customLog } = require('../../../core/utils/logger')

class GoogleTTSService extends ITTSService {
  constructor(config) {
    super()
    this.config = config || {}
    this.axiosInstance = null

    this.init()
  }

  init() {
    try {
      // Validate configuration
      if (!this.config.baseURL) {
        throw new Error('TTS service base URL not configured')
      }

      if (!this.config.apiKey) {
        throw new Error('TTS service API key not configured')
      }

      // Create axios instance for TTS service
      this.axiosInstance = axios.create({
        baseURL: this.config.baseURL,
        headers: {
          Authorization: this.config.apiKey
        },
        timeout: this.config.timeout || 30000,
        maxContentLength: this.config.maxContentLength || 10 * 1024 * 1024 // 10MB
      })

      customLog('Google TTS Service initialized successfully')

    } catch (error) {
      customLog('Error initializing Google TTS Service:', error.message)
      throw error
    }
  }

  async generateSpeech(text) {
    // Use interface validation
    const validatedText = this.validateTextInput(text)

    if (!this.axiosInstance) {
      throw new Error('TTS service not initialized')
    }

    try {
      // Clean text for better pronunciation using interface method
      const cleanedText = this.cleanTextForSpeech(validatedText)

      customLog('Generating speech with Google TTS for text length:', cleanedText.length)

      const response = await this.axiosInstance.post(
        '/t2s',
        {
          input_text: cleanedText,
          voice_params: {
            voice_id: this.config.voiceId || 'Ximena'
          },
          output_format: this.config.outputFormat || 'mp3'
        },
        {
          responseType: 'arraybuffer'
        }
      )

      // Validate response
      if (!response.data) {
        throw new Error('Empty response from speech service')
      }

      if (response.data.byteLength === 0) {
        throw new Error('Speech service returned empty audio data')
      }

      const audioBuffer = Buffer.from(response.data, 'binary')

      // Use interface validation
      const validatedBuffer = this.validateAudioOutput(audioBuffer)

      customLog('Google TTS: Speech generation completed, audio length:', validatedBuffer.length)
      return validatedBuffer

    } catch (error) {
      customLog('Error generating speech with Google TTS:', error.message || error)

      // Use interface error handling
      throw this.handleTTSError(error)
    }
  }

  async testConnection() {
    try {
      const testAudio = await this.generateSpeech('Hola, esta es una prueba del servicio de síntesis de voz.')
      customLog('Google TTS connection test successful, audio length:', testAudio.length)
      return true
    } catch (error) {
      customLog('Google TTS connection test failed:', error.message)
      return false
    }
  }

  async getAvailableVoices() {
    try {
      if (!this.axiosInstance) {
        throw new Error('TTS service not initialized')
      }

      // This would depend on your specific TTS service API
      // Returning common Spanish voices as example
      return [
        {
          name: 'Ximena',
          displayName: 'Ximena (Spanish Female)',
          gender: 'female',
          locale: 'es-ES',
          provider: 'google'
        },
        {
          name: 'Carlos',
          displayName: 'Carlos (Spanish Male)',
          gender: 'male',
          locale: 'es-ES',
          provider: 'google'
        },
        {
          name: 'Lucia',
          displayName: 'Lucia (Spanish Female)',
          gender: 'female',
          locale: 'es-MX',
          provider: 'google'
        }
      ]

    } catch (error) {
      customLog('Error getting available voices:', error.message)
      throw error
    }
  }

  getStatus() {
    return {
      provider: 'google',
      service: 'custom-tts',
      isInitialized: this.axiosInstance !== null,
      baseURL: this.config.baseURL,
      voiceId: this.config.voiceId || 'Ximena',
      outputFormat: this.config.outputFormat || 'mp3',
      timeout: this.config.timeout || 30000
    }
  }

  // Override interface error handling for Google TTS service specific errors
  handleTTSError(error) {
    const errorMessage = error.message || error.toString()

    // HTTP-specific error handling
    if (error.code === 'ECONNREFUSED') {
      return new Error('TTS service is unavailable. Please try again later.')
    } else if (error.code === 'ETIMEDOUT' || errorMessage.includes('timeout')) {
      return new Error('TTS generation timed out. Please try again with shorter text.')
    } else if (error.response) {
      const status = error.response.status
      if (status === 401) {
        return new Error('TTS service authentication failed. Please check API key.')
      } else if (status === 429) {
        return new Error('TTS service rate limit exceeded. Please try again later.')
      } else if (status >= 500) {
        return new Error('TTS service is experiencing issues. Please try again later.')
      } else {
        return new Error(`TTS service error (${status}): ${error.response.statusText || 'Unknown error'}`)
      }
    }

    // Fall back to base interface error handling
    return super.handleTTSError(error)
  }

  // Override interface method for Google-specific text cleaning
  cleanTextForSpeech(text) {
    // Start with base interface cleaning
    let cleanedText = super.cleanTextForSpeech(text)

    // Add Google-specific optimizations
    cleanedText = cleanedText
      // Handle specific brand names and technical terms
      .replace(/Movistar\+/g, 'Movistar Plus')
      .replace(/Google/g, 'Gúgel')
      .replace(/AI/g, 'inteligencia artificial')
      .replace(/API/g, 'A P I')
      .replace(/URL/g, 'U R L')
      .replace(/HTTP/g, 'H T T P')
      // Handle numbers and currency for better Spanish pronunciation
      .replace(/\b(\d+)%/g, '$1 por ciento')
      .replace(/\b(\d+)€/g, '$1 euros')
      .replace(/\b(\d+)\$/g, '$1 dólares')
      // Handle common abbreviations
      .replace(/\betc\./g, 'etcétera')
      .replace(/\bDr\./g, 'Doctor')
      .replace(/\bSra?\./g, 'Señora')
      .replace(/\bSr\./g, 'Señor')
      .replace(/\bVs\./g, 'versus')

    return cleanedText
  }

  // Google-specific configuration update
  updateVoiceSettings(settings) {
    const { voiceId, outputFormat, timeout, maxContentLength } = settings

    if (voiceId) this.config.voiceId = voiceId
    if (outputFormat) this.config.outputFormat = outputFormat
    if (timeout) this.config.timeout = timeout
    if (maxContentLength) this.config.maxContentLength = maxContentLength

    // Update axios instance if needed
    if (this.axiosInstance) {
      if (timeout) {
        this.axiosInstance.defaults.timeout = timeout
      }
      if (maxContentLength) {
        this.axiosInstance.defaults.maxContentLength = maxContentLength
      }
    }

    customLog('Google TTS voice settings updated:', settings)
    return this.getStatus()
  }

  // Method to get supported audio formats
  getSupportedFormats() {
    return [
      { name: 'mp3', description: 'MP3 Audio Format', provider: 'google' },
      { name: 'wav', description: 'WAV Audio Format', provider: 'google' },
      { name: 'ogg', description: 'OGG Audio Format', provider: 'google' },
      { name: 'flac', description: 'FLAC Audio Format', provider: 'google' }
    ]
  }

  // Method to estimate cost (if your service provides pricing info)
  estimateCost(text, options = {}) {
    const charCount = text.length
    const baseRate = options.baseRate || 0.001 // $0.001 per character example

    return {
      characters: charCount,
      estimatedCost: charCount * baseRate,
      currency: options.currency || 'USD',
      provider: 'google'
    }
  }

  // Method to check service health
  async healthCheck() {
    try {
      if (!this.axiosInstance) {
        return { status: 'error', message: 'Service not initialized' }
      }

      // Simple health check - attempt a minimal request
      const response = await this.axiosInstance.get('/health', {
        timeout: 5000
      })

      return {
        status: 'healthy',
        responseTime: Date.now(),
        serviceVersion: response.data?.version || 'unknown',
        provider: 'google'
      }

    } catch (error) {
      return {
        status: 'error',
        message: error.message,
        provider: 'google'
      }
    }
  }

  // Cleanup method
  cleanup() {
    try {
      if (this.axiosInstance) {
        // Cancel any pending requests
        this.axiosInstance = null
      }
      customLog('Google TTS Service cleaned up')
    } catch (error) {
      customLog('Error during Google TTS cleanup:', error.message)
    }
  }

  // Method to handle batch TTS requests
  async generateBatchSpeech(texts) {
    const results = []

    for (const text of texts) {
      try {
        const audio = await this.generateSpeech(text)
        results.push({
          text: text,
          audio: audio,
          status: 'success',
          provider: 'google'
        })
      } catch (error) {
        results.push({
          text: text,
          audio: null,
          status: 'error',
          error: error.message,
          provider: 'google'
        })
      }
    }

    return results
  }
}

module.exports = { GoogleTTSService }
