const { OpenAI } = require('openai')
const { IAIService } = require('../../interfaces/IAIService')
const { customLog } = require('../../../core/utils/logger')

class AzureAIService extends IAIService {
  constructor(config) {
    super()
    this.config = config || {}
    this.client = null

    this.init()
  }

  init() {
    try {
      // Validate configuration
      if (!this.config.endpoint) {
        throw new Error('Azure OpenAI endpoint not configured')
      }

      if (!this.config.apiKey) {
        throw new Error('Azure OpenAI API key not configured')
      }

      if (!this.config.deploymentName) {
        throw new Error('Azure OpenAI deployment name not configured')
      }

      // Initialize Azure OpenAI client
      this.client = new OpenAI({
        apiKey: this.config.apiKey,
        baseURL: `${this.config.endpoint}/openai/deployments/${this.config.deploymentName}`,
        defaultQuery: { 'api-version': this.config.apiVersion || '2024-08-01-preview' },
        defaultHeaders: {
          'api-key': this.config.apiKey,
        }
      })

      customLog('Azure OpenAI Service initialized successfully')

    } catch (error) {
      customLog('Error initializing Azure OpenAI Service:', error.message)
      throw error
    }
  }

  async generateReply(conversationHistory) {
    // Use interface validation
    const validatedHistory = this.validateConversationHistory(conversationHistory)

    // Validate client is initialized
    if (!this.client) {
      throw new Error('Azure OpenAI client not initialized')
    }

    try {
      // Format conversation history for OpenAI format
      const messages = [
        {
          role: 'system',
          content: this.config.systemPrompt || 'You are a helpful assistant.'
        },
        ...validatedHistory.map(turn => ({
          role: turn.user ? 'user' : 'assistant',
          content: turn.content.trim()
        }))
      ]

      customLog('Sending request to Azure OpenAI with', messages.length, 'messages')

      // Call Azure OpenAI
      const response = await this.client.chat.completions.create({
        model: this.config.deploymentName,
        messages: messages,
        max_tokens: this.config.maxTokens || 500,
        temperature: this.config.temperature || 0.7,
        top_p: this.config.topP || 0.9,
        frequency_penalty: this.config.frequencyPenalty || 0,
        presence_penalty: this.config.presencePenalty || 0,
        stop: this.config.stop || null,
        response_format: { type: 'text' },
        stream: false
      })

      // Validate response structure
      if (!response || !response.choices || response.choices.length === 0) {
        throw new Error('Invalid response structure from Azure OpenAI')
      }

      const choice = response.choices[0]
      if (!choice.message || !choice.message.content) {
        throw new Error('Invalid message structure in Azure OpenAI response')
      }

      const responseText = choice.message.content.trim()
      if (typeof responseText !== 'string' || responseText === '') {
        throw new Error('Azure OpenAI returned empty response')
      }

      // Log usage information
      if (response.usage) {
        customLog('Azure OpenAI usage:', {
          prompt_tokens: response.usage.prompt_tokens,
          completion_tokens: response.usage.completion_tokens,
          total_tokens: response.usage.total_tokens,
          provider: 'azure'
        })
      }

      return responseText

    } catch (error) {
      customLog('Error generating Azure OpenAI reply:', error.message || error)

      // Use interface error handling
      throw this.handleProviderError(error)
    }
  }

  async testConnection() {
    try {
      if (!this.client) {
        throw new Error('Client not initialized')
      }

      const testResponse = await this.client.chat.completions.create({
        model: this.config.deploymentName,
        messages: [
          { role: 'system', content: 'You are a helpful assistant.' },
          { role: 'user', content: 'Say hello' }
        ],
        max_tokens: 10,
        temperature: 0
      })

      if (testResponse && testResponse.choices && testResponse.choices[0]) {
        customLog('Azure OpenAI connection test successful')
        return true
      }

      return false

    } catch (error) {
      customLog('Azure OpenAI connection test failed:', error.message)
      return false
    }
  }

  getStatus() {
    return {
      provider: 'azure',
      isInitialized: this.client !== null,
      endpoint: this.config.endpoint,
      deploymentName: this.config.deploymentName,
      apiVersion: this.config.apiVersion || '2024-08-01-preview',
      model: this.config.deploymentName
    }
  }

  getModelInfo() {
    return {
      provider: 'azure',
      service: 'openai',
      model: this.config.deploymentName,
      endpoint: this.config.endpoint,
      apiVersion: this.config.apiVersion || '2024-08-01-preview',
      maxTokens: this.config.maxTokens || 500,
      temperature: this.config.temperature || 0.7,
      features: [
        'chat_completions',
        'system_messages',
        'conversation_history',
        'token_usage_tracking'
      ]
    }
  }

  // Override interface error handling for Azure-specific errors
  handleProviderError(error) {
    const errorMessage = error.message || error.toString()

    // Azure-specific error handling
    if (error.status === 401) {
      return new Error('Azure OpenAI authentication failed. Please check your API key.')
    } else if (error.status === 403) {
      return new Error('Azure OpenAI access forbidden. Please check your permissions.')
    } else if (error.status === 429) {
      return new Error('Azure OpenAI rate limit exceeded. Please try again later.')
    } else if (error.status >= 500) {
      return new Error('Azure OpenAI service error. Please try again later.')
    } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      return new Error('Network error connecting to Azure OpenAI. Please check your connection.')
    }

    // Fall back to base interface error handling
    return super.handleProviderError(error)
  }

  // Azure-specific configuration method
  updateConfiguration(newConfig) {
    const updatedConfig = { ...this.config, ...newConfig }

    // Only reinitialize if critical config changed
    const criticalFields = ['endpoint', 'apiKey', 'deploymentName']
    const shouldReinitialize = criticalFields.some(field =>
      this.config[field] !== updatedConfig[field]
    )

    this.config = updatedConfig

    if (shouldReinitialize) {
      customLog('Critical configuration changed, reinitializing Azure OpenAI client')
      this.init()
    }

    return this.getStatus()
  }

  // Method to get token estimate (useful for cost calculation)
  estimateTokens(text) {
    // Rough estimation: ~4 characters per token for most languages
    return Math.ceil(text.length / 4)
  }

  // Method to validate deployment availability
  async validateDeployment() {
    try {
      const response = await this.client.models.list()
      customLog('Available models/deployments:', response.data?.map(m => m.id) || [])
      return true
    } catch (error) {
      customLog('Error validating deployment:', error.message)
      return false
    }
  }
}

module.exports = { AzureAIService }
