const sdk = require('microsoft-cognitiveservices-speech-sdk')
const { ISpeechService } = require('../../interfaces/ISpeechService')
const { customLog } = require('../../../core/utils/logger')

class AzureSpeechService extends ISpeechService {
  constructor(config) {
    super()
    this.config = config || {}
    this.speechConfig = null
    this.audioConfig = null
    this.recognizer = null
    this.pushStream = null
    this.isRecognizing = false

    this.init()
  }

  init() {
    try {
      // Validate configuration
      if (!this.config.subscriptionKey) {
        throw new Error('Azure Speech subscription key not configured')
      }

      if (!this.config.region) {
        throw new Error('Azure Speech region not configured')
      }

      // Create speech configuration
      this.speechConfig = sdk.SpeechConfig.fromSubscription(
        this.config.subscriptionKey,
        this.config.region
      )

      // Configure speech recognition settings
      this.speechConfig.speechRecognitionLanguage = this.config.language || 'es-ES'
      this.speechConfig.outputFormat = sdk.OutputFormat[this.config.format || 'Detailed']
      this.speechConfig.setProfanity(sdk.ProfanityOption[this.config.profanity || 'Masked'])

      // Enable additional features
      this.speechConfig.requestWordLevelTimestamps()

      if (this.config.enableDictation) {
        this.speechConfig.enableDictation()
      }

      customLog('Azure Speech Service initialized successfully')

    } catch (error) {
      customLog('Error initializing Azure Speech Service:', error.message)
      throw error
    }
  }

  createRecognizer(onResult, onError, onSessionStopped) {
    try {
      // Clean up existing recognizer
      this.cleanup()

      // Create push stream for audio input
      this.pushStream = sdk.AudioInputStream.createPushStream()

      // Create audio configuration from push stream
      this.audioConfig = sdk.AudioConfig.fromStreamInput(this.pushStream)

      // Create speech recognizer
      this.recognizer = new sdk.SpeechRecognizer(this.speechConfig, this.audioConfig)

      // Configure event handlers
      this.recognizer.recognizing = (s, e) => {
        if (e.result.reason === sdk.ResultReason.RecognizingSpeech) {
          const text = e.result.text.trim()
          if (text && onResult) {
            onResult({
              text: text,
              isFinal: false,
              confidence: e.result.properties?.getProperty(sdk.PropertyId.SpeechServiceResponse_JsonResult),
              provider: 'azure'
            })
          }
        }
      }

      this.recognizer.recognized = (s, e) => {
        if (e.result.reason === sdk.ResultReason.RecognizedSpeech) {
          const text = e.result.text.trim()
          if (text && onResult) {
            onResult({
              text: text,
              isFinal: true,
              confidence: e.result.properties?.getProperty(sdk.PropertyId.SpeechServiceResponse_JsonResult),
              duration: e.result.duration,
              offset: e.result.offset,
              provider: 'azure'
            })
          }
        } else if (e.result.reason === sdk.ResultReason.NoMatch) {
          customLog('Azure Speech: No speech could be recognized')
        }
      }

      this.recognizer.canceled = (s, e) => {
        customLog('Azure Speech recognition canceled:', e.reason)

        if (e.reason === sdk.CancellationReason.Error) {
          const errorMessage = `Speech recognition error: ${e.errorDetails}`
          customLog(errorMessage)
          if (onError) {
            onError(new Error(errorMessage))
          }
        }

        this.stopRecognition()
      }

      this.recognizer.sessionStopped = (s, e) => {
        customLog('Azure Speech session stopped')
        this.isRecognizing = false
        if (onSessionStopped) {
          onSessionStopped()
        }
      }

      this.recognizer.sessionStarted = (s, e) => {
        customLog('Azure Speech session started')
        this.isRecognizing = true
      }

      return this.recognizer

    } catch (error) {
      customLog('Error creating Azure Speech recognizer:', error.message)
      throw error
    }
  }

  startContinuousRecognition() {
    if (!this.recognizer) {
      throw new Error('Speech recognizer not initialized')
    }

    try {
      this.recognizer.startContinuousRecognitionAsync(
        () => {
          customLog('Azure Speech continuous recognition started')
          this.isRecognizing = true
        },
        (error) => {
          customLog('Error starting Azure Speech recognition:', error)
          this.isRecognizing = false
          throw new Error(`Failed to start speech recognition: ${error}`)
        }
      )
    } catch (error) {
      customLog('Error in startContinuousRecognition:', error.message)
      throw error
    }
  }

  stopRecognition() {
    if (this.recognizer && this.isRecognizing) {
      try {
        this.recognizer.stopContinuousRecognitionAsync(
          () => {
            customLog('Azure Speech recognition stopped')
            this.isRecognizing = false
          },
          (error) => {
            customLog('Error stopping Azure Speech recognition:', error)
          }
        )
      } catch (error) {
        customLog('Error in stopRecognition:', error.message)
      }
    }
  }

  writeAudioData(audioBuffer) {
    // Use interface validation
    this.validateAudioData(audioBuffer)

    if (!this.pushStream) {
      throw new Error('Push stream not initialized')
    }

    try {
      // Write audio data to push stream
      this.pushStream.write(audioBuffer)

    } catch (error) {
      customLog('Error writing audio data to Azure Speech stream:', error.message)
      throw error
    }
  }

  closeStream() {
    if (this.pushStream) {
      try {
        this.pushStream.close()
        customLog('Azure Speech push stream closed')
      } catch (error) {
        customLog('Error closing Azure Speech stream:', error.message)
      }
    }
  }

  cleanup() {
    try {
      this.stopRecognition()

      if (this.recognizer) {
        this.recognizer.close()
        this.recognizer = null
      }

      this.closeStream()
      this.pushStream = null

      if (this.audioConfig) {
        this.audioConfig.close()
        this.audioConfig = null
      }

      customLog('Azure Speech Service cleaned up')

    } catch (error) {
      customLog('Error during Azure Speech cleanup:', error.message)
    }
  }

  isReady() {
    return this.speechConfig !== null && !this.isRecognizing
  }

  getRecognitionState() {
    return {
      isRecognizing: this.isRecognizing,
      hasRecognizer: this.recognizer !== null,
      hasStream: this.pushStream !== null,
      provider: 'azure'
    }
  }

  getConfig() {
    return {
      provider: 'azure',
      region: this.config.region,
      language: this.config.language || 'es-ES',
      format: this.config.format || 'Detailed',
      profanity: this.config.profanity || 'Masked',
      enableDictation: this.config.enableDictation || false
    }
  }

  // Azure-specific method for testing connection
  async testConnection() {
    try {
      if (!this.speechConfig) {
        throw new Error('Speech config not initialized')
      }

      // Create a temporary recognizer for testing
      const testStream = sdk.AudioInputStream.createPushStream()
      const testAudioConfig = sdk.AudioConfig.fromStreamInput(testStream)
      const testRecognizer = new sdk.SpeechRecognizer(this.speechConfig, testAudioConfig)

      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          testRecognizer.close()
          testStream.close()
          testAudioConfig.close()
          resolve(true) // If no immediate error, consider it working
        }, 1000)

        testRecognizer.sessionStarted = () => {
          clearTimeout(timeout)
          testRecognizer.close()
          testStream.close()
          testAudioConfig.close()
          resolve(true)
        }

        testRecognizer.canceled = (s, e) => {
          clearTimeout(timeout)
          testRecognizer.close()
          testStream.close()
          testAudioConfig.close()
          if (e.reason === sdk.CancellationReason.Error) {
            reject(new Error(`Connection test failed: ${e.errorDetails}`))
          } else {
            resolve(true)
          }
        }

        // Start a brief recognition session
        testRecognizer.startContinuousRecognitionAsync()
      })

    } catch (error) {
      customLog('Azure Speech connection test failed:', error.message)
      return false
    }
  }
}

module.exports = { AzureSpeechService }
