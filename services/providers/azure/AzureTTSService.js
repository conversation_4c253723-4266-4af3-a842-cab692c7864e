const sdk = require('microsoft-cognitiveservices-speech-sdk')
const { ITTSService } = require('../../interfaces/ITTSService')
const { customLog } = require('../../../core/utils/logger')

class AzureTTSService extends ITTSService {
  constructor(config) {
    super()
    this.config = config || {}
    this.speechConfig = null

    this.init()
  }

  init() {
    try {
      // Validate configuration
      if (!this.config.subscriptionKey) {
        throw new Error('Azure Speech subscription key not configured for TTS')
      }

      if (!this.config.region) {
        throw new Error('Azure Speech region not configured for TTS')
      }

      // Create speech configuration for TTS
      this.speechConfig = sdk.SpeechConfig.fromSubscription(
        this.config.subscriptionKey,
        this.config.region
      )

      // Configure TTS settings
      this.speechConfig.speechSynthesisVoiceName = this.config.voice || 'es-ES-ElviraNeural'
      this.speechConfig.speechSynthesisOutputFormat = sdk.SpeechSynthesisOutputFormat[
        this.config.outputFormat || 'Audio16Khz32KBitRateMonoMp3'
      ]

      customLog('Azure TTS Service initialized successfully with voice:', this.config.voice)

    } catch (error) {
      customLog('Error initializing Azure TTS Service:', error.message)
      throw error
    }
  }

  async generateSpeech(text) {
    // Use interface validation
    const validatedText = this.validateTextInput(text)

    if (!this.speechConfig) {
      throw new Error('Azure TTS not initialized')
    }

    try {
      // Clean text for better pronunciation using interface method
      const cleanedText = this.cleanTextForSpeech(validatedText)

      // Create SSML for better control
      const ssml = this.createAzureSSML(cleanedText)

      // Create synthesizer with null audio config to get audio data
      const synthesizer = new sdk.SpeechSynthesizer(this.speechConfig, null)

      return new Promise((resolve, reject) => {
        synthesizer.speakSsmlAsync(
          ssml,
          (result) => {
            try {
              if (result.reason === sdk.ResultReason.SynthesizingAudioCompleted) {
                customLog('Azure TTS: Speech synthesis completed successfully')

                // Convert ArrayBuffer to Buffer
                const audioBuffer = Buffer.from(result.audioData)

                // Use interface validation
                const validatedBuffer = this.validateAudioOutput(audioBuffer)

                synthesizer.close()
                resolve(validatedBuffer)

              } else if (result.reason === sdk.ResultReason.Canceled) {
                const cancellation = sdk.CancellationDetails.fromResult(result)
                const errorMessage = `Azure TTS canceled: ${cancellation.reason} - ${cancellation.errorDetails}`
                customLog('Azure TTS error:', errorMessage)

                synthesizer.close()
                reject(new Error(errorMessage))

              } else {
                const errorMessage = `Azure TTS unexpected result: ${result.reason}`
                customLog('Azure TTS error:', errorMessage)

                synthesizer.close()
                reject(new Error(errorMessage))
              }
            } catch (error) {
              synthesizer.close()
              reject(error)
            }
          },
          (error) => {
            customLog('Azure TTS synthesis error:', error)
            synthesizer.close()
            reject(new Error(`Azure TTS synthesis failed: ${error}`))
          }
        )
      })

    } catch (error) {
      customLog('Error generating speech with Azure TTS:', error.message)

      // Use interface error handling
      throw this.handleTTSError(error)
    }
  }

  // Azure-specific SSML creation
  createAzureSSML(text) {
    const voice = this.config.voice || 'es-ES-ElviraNeural'
    const speakingRate = this.config.speakingRate || '0%'
    const pitch = this.config.pitch || '0%'
    const volume = this.config.volume || '0%'

    return `
      <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="es-ES">
        <voice name="${voice}">
          <prosody rate="${speakingRate}" pitch="${pitch}" volume="${volume}">
            ${this.escapeXml(text)}
          </prosody>
        </voice>
      </speak>
    `.trim()
  }

  // Escape XML special characters for SSML
  escapeXml(text) {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&apos;')
  }

  async testConnection() {
    try {
      const testAudio = await this.generateSpeech('Hola, esta es una prueba de Azure Text-to-Speech.')
      customLog('Azure TTS connection test successful, audio length:', testAudio.length)
      return true
    } catch (error) {
      customLog('Azure TTS connection test failed:', error.message)
      return false
    }
  }

  async getAvailableVoices() {
    try {
      const synthesizer = new sdk.SpeechSynthesizer(this.speechConfig, null)

      return new Promise((resolve, reject) => {
        synthesizer.getVoicesAsync(
          'es-ES', // Language filter
          (result) => {
            try {
              if (result.reason === sdk.ResultReason.VoicesListRetrieved) {
                const voices = result.voices.map(voice => ({
                  name: voice.name,
                  displayName: voice.localName,
                  gender: voice.gender,
                  locale: voice.locale,
                  provider: 'azure'
                }))
                synthesizer.close()
                resolve(voices)
              } else {
                synthesizer.close()
                reject(new Error('Failed to retrieve voices'))
              }
            } catch (error) {
              synthesizer.close()
              reject(error)
            }
          },
          (error) => {
            synthesizer.close()
            reject(new Error(`Error retrieving voices: ${error}`))
          }
        )
      })
    } catch (error) {
      customLog('Error getting available voices:', error.message)
      throw error
    }
  }

  getStatus() {
    return {
      provider: 'azure',
      isInitialized: this.speechConfig !== null,
      voice: this.config.voice || 'es-ES-ElviraNeural',
      region: this.config.region,
      outputFormat: this.config.outputFormat || 'Audio16Khz32KBitRateMonoMp3',
      speakingRate: this.config.speakingRate || '0%',
      pitch: this.config.pitch || '0%'
    }
  }

  // Override interface method for Azure-specific text cleaning
  cleanTextForSpeech(text) {
    // Start with base interface cleaning
    let cleanedText = super.cleanTextForSpeech(text)

    // Add Azure-specific optimizations
    cleanedText = cleanedText
      // Handle brand names for better pronunciation in Spanish
      .replace(/Movistar\+/g, 'Movistar Plus')
      .replace(/AI/g, 'inteligencia artificial')
      .replace(/API/g, 'A P I')
      .replace(/URL/g, 'U R L')
      .replace(/HTTP/g, 'H T T P')
      // Handle numbers for better Spanish pronunciation
      .replace(/\b(\d+)%/g, '$1 por ciento')
      .replace(/\b(\d+)€/g, '$1 euros')
      .replace(/\b(\d+)\$/g, '$1 dólares')
      // Handle common abbreviations
      .replace(/\betc\./g, 'etcétera')
      .replace(/\bDr\./g, 'Doctor')
      .replace(/\bSra?\./g, 'Señora')
      .replace(/\bSr\./g, 'Señor')

    return cleanedText
  }

  // Azure-specific configuration update
  updateVoiceSettings(settings) {
    const { voice, speakingRate, pitch, volume, outputFormat } = settings

    if (voice) this.config.voice = voice
    if (speakingRate) this.config.speakingRate = speakingRate
    if (pitch) this.config.pitch = pitch
    if (volume) this.config.volume = volume
    if (outputFormat) this.config.outputFormat = outputFormat

    // Update speech config if it exists
    if (this.speechConfig) {
      if (voice) {
        this.speechConfig.speechSynthesisVoiceName = voice
      }
      if (outputFormat) {
        this.speechConfig.speechSynthesisOutputFormat = sdk.SpeechSynthesisOutputFormat[outputFormat]
      }
    }

    customLog('Azure TTS voice settings updated:', settings)
    return this.getStatus()
  }

  // Method to get supported audio formats
  getSupportedFormats() {
    return [
      'Audio16Khz32KBitRateMonoMp3',
      'Audio24Khz48KBitRateMonoMp3',
      'Audio24Khz160KBitRateMonoMp3',
      'Riff16Khz16BitMonoPcm',
      'Riff24Khz16BitMonoPcm',
      'Audio16Khz128KBitRateMonoMp3',
      'Audio16Khz64KBitRateMonoMp3',
      'Audio16Khz32KBitRateMonoMp3'
    ].map(format => ({
      name: format,
      provider: 'azure'
    }))
  }

  // Cleanup method
  cleanup() {
    try {
      if (this.speechConfig) {
        // Speech config doesn't need explicit cleanup
        this.speechConfig = null
      }
      customLog('Azure TTS Service cleaned up')
    } catch (error) {
      customLog('Error during Azure TTS cleanup:', error.message)
    }
  }
}

module.exports = { AzureTTSService }
